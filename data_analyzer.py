"""
股票数据分析与预处理模块
包含数据清洗、特征工程和技术指标计算功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import ta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['mathtext.fontset'] = 'stix'

class StockDataAnalyzer:
    """股票数据分析器"""
    
    def __init__(self, data_path: str):
        """
        初始化数据分析器
        
        Args:
            data_path: CSV数据文件路径
        """
        self.data_path = data_path
        self.raw_data = None
        self.processed_data = None
        self.stock_list = None
        
    def load_data(self) -> pd.DataFrame:
        """加载原始数据"""
        print("正在加载数据...")
        self.raw_data = pd.read_csv(self.data_path, encoding='utf-8')
        
        # 数据类型转换
        self.raw_data['交易日期'] = pd.to_datetime(self.raw_data['交易日期'])
        numeric_columns = ['开盘价', '最高价', '最低价', '收盘价', '成交量']
        for col in numeric_columns:
            self.raw_data[col] = pd.to_numeric(self.raw_data[col], errors='coerce')
        
        # 按股票代码和日期排序
        self.raw_data = self.raw_data.sort_values(['股票代码', '交易日期'])
        
        print(f"数据加载完成: {len(self.raw_data)}行, {self.raw_data['股票代码'].nunique()}只股票")
        return self.raw_data
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        df = df.copy()
        
        # 基础价格指标
        df['价格变化率'] = df['收盘价'].pct_change()
        df['振幅'] = (df['最高价'] - df['最低价']) / df['收盘价']
        df['上影线比例'] = (df['最高价'] - df[['开盘价', '收盘价']].max(axis=1)) / df['收盘价']
        df['下影线比例'] = (df[['开盘价', '收盘价']].min(axis=1) - df['最低价']) / df['收盘价']
        
        # 移动平均线
        for period in [5, 10, 20, 50]:
            df[f'MA{period}'] = df['收盘价'].rolling(window=period).mean()
            df[f'MA{period}_ratio'] = df['收盘价'] / df[f'MA{period}'] - 1
        
        # 成交量指标
        df['成交量_MA5'] = df['成交量'].rolling(window=5).mean()
        df['成交量比率'] = df['成交量'] / df['成交量_MA5']
        
        # RSI指标
        df['RSI'] = ta.momentum.RSIIndicator(df['收盘价'], window=14).rsi()
        
        # MACD指标
        macd = ta.trend.MACD(df['收盘价'])
        df['MACD'] = macd.macd()
        df['MACD_signal'] = macd.macd_signal()
        df['MACD_histogram'] = macd.macd_diff()
        
        # 布林带
        bollinger = ta.volatility.BollingerBands(df['收盘价'], window=20)
        df['BB_upper'] = bollinger.bollinger_hband()
        df['BB_lower'] = bollinger.bollinger_lband()
        df['BB_position'] = (df['收盘价'] - df['BB_lower']) / (df['BB_upper'] - df['BB_lower'])
        
        # 价格位置（相对于历史高低点）
        df['价格位置_52周'] = (df['收盘价'] - df['收盘价'].rolling(52).min()) / \
                        (df['收盘价'].rolling(52).max() - df['收盘价'].rolling(52).min())
        
        return df
    
    def calculate_future_returns(self, df: pd.DataFrame, periods: List[int] = [10, 20, 50]) -> pd.DataFrame:
        """计算未来收益率"""
        df = df.copy()

        for period in periods:
            # 未来收益率
            df[f'future_return_{period}w'] = df['收盘价'].shift(-period) / df['收盘价'] - 1
            # 是否翻倍
            df[f'double_{period}w'] = (df[f'future_return_{period}w'] >= 1.0).astype(int)

            # 计算未来期间的最大回撤
            future_min_prices = []
            for i in range(len(df)):
                if i + period < len(df):
                    future_slice = df['收盘价'].iloc[i+1:i+period+1]
                    if len(future_slice) > 0:
                        min_price = future_slice.min()
                        future_min_prices.append(min_price)
                    else:
                        future_min_prices.append(np.nan)
                else:
                    future_min_prices.append(np.nan)

            df[f'max_drawdown_{period}w'] = (np.array(future_min_prices) / df['收盘价'] - 1)

        return df
    
    def process_all_stocks(self) -> pd.DataFrame:
        """处理所有股票数据"""
        if self.raw_data is None:
            self.load_data()
        
        print("正在计算技术指标和未来收益...")
        processed_stocks = []
        
        for stock_code in self.raw_data['股票代码'].unique():
            stock_data = self.raw_data[self.raw_data['股票代码'] == stock_code].copy()
            
            # 计算技术指标
            stock_data = self.calculate_technical_indicators(stock_data)
            
            # 计算未来收益
            stock_data = self.calculate_future_returns(stock_data)
            
            processed_stocks.append(stock_data)
        
        self.processed_data = pd.concat(processed_stocks, ignore_index=True)
        
        # 移除包含NaN的行（主要是开始和结束的数据）
        self.processed_data = self.processed_data.dropna()
        
        print(f"数据处理完成: {len(self.processed_data)}行有效数据")
        return self.processed_data
    
    def get_feature_columns(self) -> List[str]:
        """获取特征列名"""
        feature_cols = [
            '价格变化率', '振幅', '上影线比例', '下影线比例',
            'MA5_ratio', 'MA10_ratio', 'MA20_ratio', 'MA50_ratio',
            '成交量比率', 'RSI', 'MACD', 'MACD_signal', 'MACD_histogram',
            'BB_position', '价格位置_52周'
        ]
        return feature_cols
    
    def analyze_data_quality(self) -> Dict:
        """分析数据质量"""
        if self.processed_data is None:
            self.process_all_stocks()
        
        analysis = {
            'total_records': len(self.processed_data),
            'unique_stocks': self.processed_data['股票代码'].nunique(),
            'date_range': (self.processed_data['交易日期'].min(), self.processed_data['交易日期'].max()),
            'missing_values': self.processed_data.isnull().sum().to_dict(),
            'double_rate_50w': self.processed_data['double_50w'].mean() if 'double_50w' in self.processed_data.columns else 0
        }
        
        return analysis
    
    def plot_data_overview(self, save_path: Optional[str] = None):
        """绘制数据概览图"""
        if self.processed_data is None:
            self.process_all_stocks()
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('股票数据概览分析', fontsize=16)
        
        # 1. 翻倍概率分布
        double_rates = []
        for period in [10, 20, 50]:
            if f'double_{period}w' in self.processed_data.columns:
                rate = self.processed_data[f'double_{period}w'].mean()
                double_rates.append(rate)
        
        axes[0, 0].bar(['10周', '20周', '50周'], double_rates)
        axes[0, 0].set_title('不同时间窗口翻倍概率')
        axes[0, 0].set_ylabel('翻倍概率')
        
        # 2. 价格变化率分布
        axes[0, 1].hist(self.processed_data['价格变化率'].dropna(), bins=50, alpha=0.7)
        axes[0, 1].set_title('周价格变化率分布')
        axes[0, 1].set_xlabel('价格变化率')
        
        # 3. RSI分布
        axes[1, 0].hist(self.processed_data['RSI'].dropna(), bins=30, alpha=0.7)
        axes[1, 0].set_title('RSI指标分布')
        axes[1, 0].set_xlabel('RSI')
        
        # 4. 成交量比率分布
        volume_ratio = self.processed_data['成交量比率'].dropna()
        volume_ratio = volume_ratio[volume_ratio < 10]  # 过滤异常值
        axes[1, 1].hist(volume_ratio, bins=30, alpha=0.7)
        axes[1, 1].set_title('成交量比率分布')
        axes[1, 1].set_xlabel('成交量比率')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def get_current_stock_features(self) -> pd.DataFrame:
        """获取所有股票的最新特征数据"""
        if self.processed_data is None:
            self.process_all_stocks()
        
        # 获取每只股票的最新数据
        latest_data = self.processed_data.groupby('股票代码').last().reset_index()
        
        feature_cols = self.get_feature_columns()
        result_cols = ['股票代码', '股票名称', '交易日期', '收盘价'] + feature_cols
        
        return latest_data[result_cols]

if __name__ == "__main__":
    # 测试数据分析器
    analyzer = StockDataAnalyzer('缺口股票时序数据集_20250625_213940.csv')
    
    # 加载和处理数据
    analyzer.load_data()
    analyzer.process_all_stocks()
    
    # 分析数据质量
    quality_analysis = analyzer.analyze_data_quality()
    print("\n数据质量分析:")
    for key, value in quality_analysis.items():
        print(f"{key}: {value}")
    
    # 绘制数据概览
    analyzer.plot_data_overview('data_overview.png')
    
    # 获取最新特征数据
    current_features = analyzer.get_current_stock_features()
    print(f"\n当前特征数据: {len(current_features)}只股票")
    print(current_features.head())
