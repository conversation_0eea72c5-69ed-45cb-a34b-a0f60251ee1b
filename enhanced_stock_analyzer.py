"""
增强股票数据分析器
专注于翻倍行情识别、波段分析和特征工程
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import ta
from typing import Dict, List, Tuple, Optional
import warnings
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import mutual_info_regression
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

warnings.filterwarnings('ignore')

# 设置中文字体和数学字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['mathtext.fontset'] = 'stix'

class EnhancedStockAnalyzer:
    """增强股票数据分析器"""
    
    def __init__(self, data_path: str):
        """
        初始化增强分析器
        
        Args:
            data_path: CSV数据文件路径
        """
        self.data_path = data_path
        self.raw_data = None
        self.processed_data = None
        self.double_stocks = None
        self.feature_correlation = None
        
    def load_and_preprocess_data(self) -> pd.DataFrame:
        """加载和预处理数据"""
        print("正在加载数据...")
        self.raw_data = pd.read_csv(self.data_path, encoding='utf-8')
        
        # 数据类型转换
        self.raw_data['交易日期'] = pd.to_datetime(self.raw_data['交易日期'])
        numeric_columns = ['开盘价', '最高价', '最低价', '收盘价', '成交量']
        for col in numeric_columns:
            self.raw_data[col] = pd.to_numeric(self.raw_data[col], errors='coerce')
        
        # 按股票代码和日期排序
        self.raw_data = self.raw_data.sort_values(['股票代码', '交易日期'])
        
        print(f"数据加载完成: {len(self.raw_data)}行, {self.raw_data['股票代码'].nunique()}只股票")
        return self.raw_data
    
    def identify_double_opportunities(self, periods: List[int] = [10, 20, 30, 50]) -> Dict:
        """识别翻倍机会"""
        print("正在识别翻倍行情...")
        
        double_analysis = {}
        all_stocks = []
        
        for stock_code in self.raw_data['股票代码'].unique():
            stock_data = self.raw_data[self.raw_data['股票代码'] == stock_code].copy()
            stock_data = stock_data.sort_values('交易日期')
            
            stock_info = {
                '股票代码': stock_code,
                '股票名称': stock_data['股票名称'].iloc[0],
                '数据点数': len(stock_data)
            }
            
            # 计算不同周期的翻倍情况
            for period in periods:
                future_prices = stock_data['收盘价'].shift(-period)
                current_prices = stock_data['收盘价']
                returns = (future_prices / current_prices - 1) * 100
                
                # 翻倍机会（100%以上涨幅）
                double_opportunities = (returns >= 100).sum()
                max_return = returns.max() if not returns.isna().all() else 0
                
                stock_info[f'{period}周翻倍次数'] = double_opportunities
                stock_info[f'{period}周最大涨幅'] = max_return
                stock_info[f'{period}周翻倍概率'] = double_opportunities / len(stock_data) * 100
            
            all_stocks.append(stock_info)
        
        double_analysis['股票统计'] = pd.DataFrame(all_stocks)
        
        # 整体统计
        summary = {}
        for period in periods:
            total_doubles = double_analysis['股票统计'][f'{period}周翻倍次数'].sum()
            stocks_with_doubles = (double_analysis['股票统计'][f'{period}周翻倍次数'] > 0).sum()
            avg_double_prob = double_analysis['股票统计'][f'{period}周翻倍概率'].mean()
            
            summary[f'{period}周'] = {
                '总翻倍机会数': total_doubles,
                '有翻倍机会的股票数': stocks_with_doubles,
                '平均翻倍概率': avg_double_prob,
                '翻倍股票占比': stocks_with_doubles / len(double_analysis['股票统计']) * 100
            }
        
        double_analysis['整体统计'] = summary
        self.double_stocks = double_analysis
        
        return double_analysis
    
    def calculate_enhanced_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算增强技术指标"""
        df = df.copy()
        
        # 基础价格指标
        df['价格变化率'] = df['收盘价'].pct_change()
        df['振幅'] = (df['最高价'] - df['最低价']) / df['收盘价']
        df['上影线比例'] = (df['最高价'] - df[['开盘价', '收盘价']].max(axis=1)) / df['收盘价']
        df['下影线比例'] = (df[['开盘价', '收盘价']].min(axis=1) - df['最低价']) / df['收盘价']
        
        # 移动平均线系统
        for period in [5, 10, 20, 50, 100]:
            df[f'MA{period}'] = df['收盘价'].rolling(window=period).mean()
            df[f'MA{period}_ratio'] = df['收盘价'] / df[f'MA{period}'] - 1
        
        # 成交量指标
        df['成交量_MA5'] = df['成交量'].rolling(window=5).mean()
        df['成交量_MA20'] = df['成交量'].rolling(window=20).mean()
        df['成交量比率_5'] = df['成交量'] / df['成交量_MA5']
        df['成交量比率_20'] = df['成交量'] / df['成交量_MA20']
        
        # RSI指标
        df['RSI_14'] = ta.momentum.RSIIndicator(df['收盘价'], window=14).rsi()
        df['RSI_6'] = ta.momentum.RSIIndicator(df['收盘价'], window=6).rsi()
        
        # MACD指标
        macd = ta.trend.MACD(df['收盘价'])
        df['MACD'] = macd.macd()
        df['MACD_signal'] = macd.macd_signal()
        df['MACD_histogram'] = macd.macd_diff()
        
        # 布林带
        bollinger = ta.volatility.BollingerBands(df['收盘价'], window=20)
        df['BB_upper'] = bollinger.bollinger_hband()
        df['BB_lower'] = bollinger.bollinger_lband()
        df['BB_middle'] = bollinger.bollinger_mavg()
        df['BB_position'] = (df['收盘价'] - df['BB_lower']) / (df['BB_upper'] - df['BB_lower'])
        df['BB_width'] = (df['BB_upper'] - df['BB_lower']) / df['BB_middle']
        
        # KDJ指标
        stoch = ta.momentum.StochasticOscillator(df['最高价'], df['最低价'], df['收盘价'])
        df['K'] = stoch.stoch()
        df['D'] = stoch.stoch_signal()
        df['J'] = 3 * df['K'] - 2 * df['D']
        
        # 威廉指标
        df['WR'] = ta.momentum.WilliamsRIndicator(df['最高价'], df['最低价'], df['收盘价']).williams_r()
        
        # CCI指标
        df['CCI'] = ta.trend.CCIIndicator(df['最高价'], df['最低价'], df['收盘价']).cci()
        
        # ATR指标（真实波动幅度）
        df['ATR'] = ta.volatility.AverageTrueRange(df['最高价'], df['最低价'], df['收盘价']).average_true_range()
        df['ATR_ratio'] = df['ATR'] / df['收盘价']
        
        # ADX指标（趋势强度）
        df['ADX'] = ta.trend.ADXIndicator(df['最高价'], df['最低价'], df['收盘价']).adx()
        
        # OBV指标（能量潮）
        df['OBV'] = ta.volume.OnBalanceVolumeIndicator(df['收盘价'], df['成交量']).on_balance_volume()
        df['OBV_MA'] = df['OBV'].rolling(window=20).mean()
        df['OBV_ratio'] = df['OBV'] / df['OBV_MA'] - 1
        
        # 价格位置指标
        for period in [20, 50, 100]:
            df[f'价格位置_{period}周'] = (df['收盘价'] - df['收盘价'].rolling(period).min()) / \
                                    (df['收盘价'].rolling(period).max() - df['收盘价'].rolling(period).min())
        
        # 趋势强度指标
        df['趋势强度_20'] = df['收盘价'].rolling(20).apply(lambda x: stats.linregress(range(len(x)), x)[0] if len(x) == 20 else np.nan)
        df['趋势强度_50'] = df['收盘价'].rolling(50).apply(lambda x: stats.linregress(range(len(x)), x)[0] if len(x) == 50 else np.nan)
        
        return df
    
    def analyze_wave_patterns(self, df: pd.DataFrame) -> pd.DataFrame:
        """分析波段模式"""
        df = df.copy()

        # 计算价格相对位置
        df['价格百分位_20'] = df['收盘价'].rolling(20).rank(pct=True)
        df['价格百分位_50'] = df['收盘价'].rolling(50).rank(pct=True)

        # 波段状态识别
        conditions = [
            (df['价格百分位_50'] <= 0.2) & (df['RSI_14'] <= 30),  # 底部区域
            (df['价格百分位_50'] > 0.2) & (df['价格百分位_50'] <= 0.8) & (df['趋势强度_20'] > 0),  # 上升区域
            (df['价格百分位_50'] >= 0.8) & (df['RSI_14'] >= 70),  # 顶部区域
            (df['价格百分位_50'] > 0.2) & (df['价格百分位_50'] <= 0.8) & (df['趋势强度_20'] <= 0)   # 下跌区域
        ]

        choices = ['底部区域', '上升区域', '顶部区域', '下跌区域']
        df['波段状态'] = np.select(conditions, choices, default='震荡区域')

        return df

    def process_stock_with_enhanced_features(self, stock_code: str) -> pd.DataFrame:
        """处理单只股票的增强特征"""
        stock_data = self.raw_data[self.raw_data['股票代码'] == stock_code].copy()
        stock_data = stock_data.sort_values('交易日期')

        # 计算增强技术指标
        stock_data = self.calculate_enhanced_technical_indicators(stock_data)

        # 分析波段模式
        stock_data = self.analyze_wave_patterns(stock_data)

        # 计算未来收益率和翻倍标签
        for period in [10, 20, 30, 50]:
            future_prices = stock_data['收盘价'].shift(-period)
            current_prices = stock_data['收盘价']
            stock_data[f'future_return_{period}w'] = (future_prices / current_prices - 1) * 100
            stock_data[f'is_double_{period}w'] = (stock_data[f'future_return_{period}w'] >= 100).astype(int)

        return stock_data

    def calculate_feature_correlation_with_doubling(self, processed_data: pd.DataFrame) -> pd.DataFrame:
        """计算特征与翻倍概率的相关性"""
        print("正在计算特征相关性...")

        # 选择技术指标特征
        feature_columns = [
            '价格变化率', '振幅', '上影线比例', '下影线比例',
            'MA5_ratio', 'MA10_ratio', 'MA20_ratio', 'MA50_ratio', 'MA100_ratio',
            '成交量比率_5', '成交量比率_20',
            'RSI_14', 'RSI_6',
            'MACD', 'MACD_signal', 'MACD_histogram',
            'BB_position', 'BB_width',
            'K', 'D', 'J',
            'WR', 'CCI', 'ATR_ratio', 'ADX', 'OBV_ratio',
            '价格位置_20周', '价格位置_50周', '价格位置_100周',
            '趋势强度_20', '趋势强度_50',
            '价格百分位_20', '价格百分位_50'
        ]

        # 目标变量
        target_columns = ['is_double_10w', 'is_double_20w', 'is_double_30w', 'is_double_50w']

        correlation_results = []

        for target in target_columns:
            print(f"计算与{target}的相关性...")

            # 准备数据
            valid_data = processed_data[feature_columns + [target]].dropna()

            if len(valid_data) < 100:
                continue

            X = valid_data[feature_columns]
            y = valid_data[target]

            # 计算皮尔逊相关系数
            pearson_corr = {}
            for feature in feature_columns:
                if feature in X.columns:
                    corr_val = X[feature].corr(y)
                    pearson_corr[feature] = corr_val if not np.isnan(corr_val) else 0

            # 计算互信息
            try:
                # 标准化特征
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X.fillna(0))

                mi_scores = mutual_info_regression(X_scaled, y, random_state=42)
                mi_dict = dict(zip(feature_columns, mi_scores))
            except:
                mi_dict = {feature: 0 for feature in feature_columns}

            # 合并结果
            for feature in feature_columns:
                correlation_results.append({
                    '特征名称': feature,
                    '目标变量': target,
                    '皮尔逊相关系数': pearson_corr.get(feature, 0),
                    '互信息得分': mi_dict.get(feature, 0),
                    '绝对相关系数': abs(pearson_corr.get(feature, 0))
                })

        correlation_df = pd.DataFrame(correlation_results)
        self.feature_correlation = correlation_df

        return correlation_df
    
    def visualize_double_analysis(self) -> None:
        """可视化翻倍分析结果"""
        if self.double_stocks is None:
            print("请先运行 identify_double_opportunities 方法")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('股票翻倍机会分析', fontsize=16, fontweight='bold')
        
        # 1. 不同周期翻倍机会分布
        periods = [10, 20, 30, 50]
        double_counts = [self.double_stocks['整体统计'][f'{p}周']['总翻倍机会数'] for p in periods]
        
        axes[0, 0].bar([f'{p}周' for p in periods], double_counts, color='skyblue', alpha=0.7)
        axes[0, 0].set_title('不同周期总翻倍机会数')
        axes[0, 0].set_ylabel('翻倍机会数')
        
        # 2. 翻倍股票占比
        stock_ratios = [self.double_stocks['整体统计'][f'{p}周']['翻倍股票占比'] for p in periods]
        
        axes[0, 1].plot([f'{p}周' for p in periods], stock_ratios, marker='o', linewidth=2, markersize=8)
        axes[0, 1].set_title('有翻倍机会的股票占比')
        axes[0, 1].set_ylabel('占比 (%)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 翻倍次数分布
        double_counts_20w = self.double_stocks['股票统计']['20周翻倍次数']
        axes[1, 0].hist(double_counts_20w, bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[1, 0].set_title('20周翻倍次数分布')
        axes[1, 0].set_xlabel('翻倍次数')
        axes[1, 0].set_ylabel('股票数量')
        
        # 4. 最大涨幅分布
        max_returns_20w = self.double_stocks['股票统计']['20周最大涨幅']
        axes[1, 1].hist(max_returns_20w, bins=30, alpha=0.7, color='orange', edgecolor='black')
        axes[1, 1].set_title('20周最大涨幅分布')
        axes[1, 1].set_xlabel('最大涨幅 (%)')
        axes[1, 1].set_ylabel('股票数量')
        
        plt.tight_layout()
        plt.savefig('翻倍机会分析.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 打印统计信息
        print("\n=== 翻倍机会统计摘要 ===")
        for period in periods:
            stats = self.double_stocks['整体统计'][f'{period}周']
            print(f"\n{period}周期:")
            print(f"  总翻倍机会数: {stats['总翻倍机会数']}")
            print(f"  有翻倍机会的股票数: {stats['有翻倍机会的股票数']}")
            print(f"  翻倍股票占比: {stats['翻倍股票占比']:.2f}%")
            print(f"  平均翻倍概率: {stats['平均翻倍概率']:.2f}%")
