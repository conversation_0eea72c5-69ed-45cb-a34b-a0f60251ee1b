"""
运行波段模式分析
"""

from wave_pattern_analyzer import WavePatternAnalyzer
import pandas as pd
import numpy as np
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

def main():
    print("=== 开始波段模式分析 ===")
    
    # 初始化波段分析器
    wave_analyzer = WavePatternAnalyzer()
    
    # 读取增强特征数据
    try:
        enhanced_data = pd.read_csv('增强特征数据.csv')
        print(f"成功读取增强特征数据: {len(enhanced_data)} 行")
    except FileNotFoundError:
        print("未找到增强特征数据文件，将重新加载原始数据...")
        # 如果没有增强特征数据，从原始数据开始
        raw_data = pd.read_csv('缺口股票时序数据集_20250625_213940.csv')
        high_potential_stocks = pd.read_csv('高翻倍潜力股票.csv')
        top_stocks = high_potential_stocks.head(20)['股票代码'].tolist()
        
        enhanced_data = []
        for stock_code in tqdm(top_stocks, desc="处理股票数据"):
            stock_data = raw_data[raw_data['股票代码'] == stock_code].copy()
            stock_data = stock_data.sort_values('交易日期')
            enhanced_data.append(stock_data)
        
        enhanced_data = pd.concat(enhanced_data, ignore_index=True)
    
    # 对所有股票进行波段分析
    print("正在进行波段模式识别...")
    
    all_wave_data = []
    stock_codes = enhanced_data['股票代码'].unique()
    
    for stock_code in tqdm(stock_codes[:10], desc="波段分析"):  # 先分析前10只股票
        try:
            stock_data = enhanced_data[enhanced_data['股票代码'] == stock_code].copy()
            stock_data = stock_data.sort_values('交易日期').reset_index(drop=True)
            
            # 进行波段识别
            wave_data = wave_analyzer.identify_wave_patterns(stock_data)
            
            # 添加翻倍标签（如果没有的话）
            if 'is_double_20w' not in wave_data.columns:
                wave_data['future_return_20w'] = wave_data['收盘价'].shift(-20) / wave_data['收盘价'] - 1
                wave_data['is_double_20w'] = (wave_data['future_return_20w'] >= 1.0).astype(int)
            
            all_wave_data.append(wave_data)
            
        except Exception as e:
            print(f"处理股票 {stock_code} 时出错: {e}")
            continue
    
    if all_wave_data:
        # 合并所有波段数据
        combined_wave_data = pd.concat(all_wave_data, ignore_index=True)
        
        # 保存波段分析结果
        combined_wave_data.to_csv('波段分析结果.csv', index=False, encoding='utf-8-sig')
        print(f"波段分析完成，共处理 {len(all_wave_data)} 只股票")
        
        # 分析波段统计信息
        wave_stats = wave_analyzer.analyze_wave_statistics(combined_wave_data)
        
        # 打印统计结果
        print_wave_statistics(wave_stats)
        
        # 创建汇总报告
        wave_analyzer.create_wave_summary_report(combined_wave_data)
        
        # 分析波段与翻倍概率的关系
        analyze_wave_doubling_relationship(combined_wave_data)
        
        # 选择一只代表性股票进行详细可视化
        if len(all_wave_data) > 0:
            sample_stock = all_wave_data[0]
            sample_code = sample_stock['股票代码'].iloc[0]
            wave_analyzer.visualize_wave_patterns(sample_stock, sample_code)
        
        # 保存波段统计信息
        save_wave_statistics(wave_stats, combined_wave_data)
        
    else:
        print("没有成功处理任何股票数据")

def print_wave_statistics(wave_stats):
    """打印波段统计信息"""
    print("\n=== 波段统计信息 ===")
    
    print("\n1. 波段分布:")
    for pattern, count in wave_stats['波段分布'].items():
        print(f"   {pattern}: {count} 个时间点")
    
    print("\n2. 平均波段强度:")
    for pattern, strength in wave_stats['平均波段强度'].items():
        print(f"   {pattern}: {strength:.3f}")
    
    print("\n3. 平均持续期:")
    for pattern, duration in wave_stats['平均持续期'].items():
        print(f"   {pattern}: {duration:.1f} 周")

def analyze_wave_doubling_relationship(wave_data):
    """分析波段与翻倍概率的关系"""
    print("\n=== 波段与翻倍概率关系分析 ===")
    
    if 'is_double_20w' in wave_data.columns:
        # 各波段的翻倍概率
        double_prob = wave_data.groupby('波段状态')['is_double_20w'].agg(['mean', 'count'])
        double_prob['翻倍概率(%)'] = double_prob['mean'] * 100
        
        print("\n各波段20周翻倍概率:")
        for pattern in double_prob.index:
            prob = double_prob.loc[pattern, '翻倍概率(%)']
            count = double_prob.loc[pattern, 'count']
            print(f"   {pattern}: {prob:.2f}% (样本数: {count})")
        
        # 波段强度与翻倍概率的关系
        print("\n波段强度与翻倍概率关系:")
        for pattern in wave_data['波段状态'].unique():
            pattern_data = wave_data[wave_data['波段状态'] == pattern]
            if len(pattern_data) > 10:
                # 按强度分组
                pattern_data['强度分组'] = pd.cut(pattern_data['波段强度'], bins=3, labels=['低', '中', '高'])
                strength_prob = pattern_data.groupby('强度分组')['is_double_20w'].mean() * 100
                
                print(f"   {pattern}:")
                for strength, prob in strength_prob.items():
                    print(f"     {strength}强度: {prob:.2f}%")

def save_wave_statistics(wave_stats, wave_data):
    """保存波段统计信息"""
    # 保存波段分布
    distribution_df = pd.DataFrame(list(wave_stats['波段分布'].items()), 
                                 columns=['波段状态', '数量'])
    distribution_df.to_csv('波段分布统计.csv', index=False, encoding='utf-8-sig')
    
    # 保存波段强度统计
    strength_df = pd.DataFrame(list(wave_stats['平均波段强度'].items()), 
                             columns=['波段状态', '平均强度'])
    strength_df.to_csv('波段强度统计.csv', index=False, encoding='utf-8-sig')
    
    # 保存翻倍概率统计
    if 'is_double_20w' in wave_data.columns:
        double_stats = wave_data.groupby('波段状态')['is_double_20w'].agg(['mean', 'count', 'sum'])
        double_stats['翻倍概率(%)'] = double_stats['mean'] * 100
        double_stats.columns = ['翻倍概率', '总样本数', '翻倍次数', '翻倍概率(%)']
        double_stats.to_csv('波段翻倍概率统计.csv', encoding='utf-8-sig')
    
    print("\n波段分析结果已保存:")
    print("- 波段分析结果.csv: 完整的波段分析数据")
    print("- 波段分布统计.csv: 波段分布统计")
    print("- 波段强度统计.csv: 波段强度统计")
    print("- 波段翻倍概率统计.csv: 各波段翻倍概率统计")
    print("- 波段分析汇总报告.png: 可视化汇总报告")

if __name__ == "__main__":
    main()
