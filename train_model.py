"""
训练股票预测模型
"""

import torch
import torch.nn as nn
import numpy as np
from sklearn.utils.class_weight import compute_class_weight
from data_analyzer import StockDataAnalyzer
from stock_transformer import StockPredictor
import warnings
warnings.filterwarnings('ignore')

def main():
    print("=== 股票翻倍概率预测模型训练 ===")
    
    # 加载数据
    print("1. 加载和处理数据...")
    analyzer = StockDataAnalyzer('缺口股票时序数据集_20250625_213940.csv')
    data = analyzer.process_all_stocks()
    
    print(f"数据概况:")
    print(f"  - 总记录数: {len(data)}")
    print(f"  - 股票数量: {data['股票代码'].nunique()}")
    print(f"  - 时间范围: {data['交易日期'].min()} 到 {data['交易日期'].max()}")
    
    # 分析翻倍概率分布
    for weeks in [10, 20, 50]:
        col = f'double_{weeks}w'
        if col in data.columns:
            rate = data[col].mean()
            print(f"  - {weeks}周翻倍概率: {rate:.3f} ({rate*100:.1f}%)")
    
    # 训练模型
    print("\n2. 训练深度学习模型...")
    predictor = StockPredictor(sequence_length=30, prediction_weeks=50)
    
    # 使用更多的训练轮次和更好的参数
    metrics = predictor.train(
        data, 
        test_size=0.2, 
        batch_size=64, 
        epochs=100, 
        learning_rate=0.0005
    )
    
    # 生成预测
    print("\n3. 生成股票预测...")
    predictions = predictor.predict(data)
    print(f"成功预测 {len(predictions)} 只股票")
    
    # 获取推荐
    recommendations = predictor.get_top_recommendations(predictions, top_n=10)
    
    print("\n=== 前10只推荐股票 ===")
    print("排名 | 股票代码 | 股票名称 | 当前价格 | 翻倍概率 | 最新日期")
    print("-" * 70)
    
    for rec in recommendations:
        print(f"{rec['rank']:2d}   | {rec['stock_code']:8s} | {rec['stock_name']:8s} | "
              f"{rec['current_price']:8.2f} | {rec['probability_percent']:7.2f}% | {rec['latest_date']}")
    
    # 保存结果
    print("\n4. 保存结果...")
    
    # 保存推荐结果到CSV
    import pandas as pd
    rec_df = pd.DataFrame(recommendations)
    rec_df.to_csv('stock_recommendations.csv', index=False, encoding='utf-8-sig')
    print("推荐结果已保存到 stock_recommendations.csv")
    
    # 保存所有预测结果
    all_predictions = []
    for stock_code, info in predictions.items():
        all_predictions.append({
            'stock_code': stock_code,
            'stock_name': info['stock_name'],
            'current_price': info['current_price'],
            'double_probability': info['double_probability'],
            'probability_percent': info['double_probability'] * 100,
            'latest_date': info['latest_date']
        })
    
    pred_df = pd.DataFrame(all_predictions)
    pred_df = pred_df.sort_values('double_probability', ascending=False)
    pred_df.to_csv('all_predictions.csv', index=False, encoding='utf-8-sig')
    print("所有预测结果已保存到 all_predictions.csv")
    
    # 模型性能总结
    print("\n=== 模型性能总结 ===")
    print(f"准确率: {metrics['accuracy']:.4f}")
    print(f"精确率: {metrics['precision']:.4f}")
    print(f"召回率: {metrics['recall']:.4f}")
    print(f"F1分数: {metrics['f1']:.4f}")
    print(f"AUC: {metrics['auc']:.4f}")
    
    # 风险提示
    print("\n=== 风险提示 ===")
    print("1. 本预测结果仅供参考，不构成投资建议")
    print("2. 股票投资存在风险，请谨慎决策")
    print("3. 模型基于历史数据训练，未来表现可能不同")
    print("4. 建议结合其他分析方法综合判断")
    
    return recommendations, metrics

if __name__ == "__main__":
    recommendations, metrics = main()
