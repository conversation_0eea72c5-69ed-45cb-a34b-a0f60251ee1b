"""
波段模式分析器
专门用于识别和划分股票价格的波段区域
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import signal
from scipy.stats import linregress
import ta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['mathtext.fontset'] = 'stix'

class WavePatternAnalyzer:
    """波段模式分析器"""
    
    def __init__(self):
        self.wave_patterns = None
        self.pattern_statistics = None
    
    def identify_wave_patterns(self, df: pd.DataFrame) -> pd.DataFrame:
        """识别波段模式"""
        df = df.copy()
        
        # 1. 计算基础技术指标（如果还没有）
        if 'RSI_14' not in df.columns:
            df['RSI_14'] = ta.momentum.RSIIndicator(df['收盘价'], window=14).rsi()
        
        if 'MACD' not in df.columns:
            macd = ta.trend.MACD(df['收盘价'])
            df['MACD'] = macd.macd()
            df['MACD_signal'] = macd.macd_signal()
        
        # 2. 计算价格位置指标
        for period in [20, 50, 100]:
            df[f'价格位置_{period}'] = (df['收盘价'] - df['收盘价'].rolling(period).min()) / \
                                   (df['收盘价'].rolling(period).max() - df['收盘价'].rolling(period).min())
        
        # 3. 计算趋势强度
        df['趋势强度_20'] = df['收盘价'].rolling(20).apply(
            lambda x: linregress(range(len(x)), x)[0] if len(x) == 20 else np.nan
        )
        df['趋势强度_50'] = df['收盘价'].rolling(50).apply(
            lambda x: linregress(range(len(x)), x)[0] if len(x) == 50 else np.nan
        )
        
        # 4. 计算移动平均线位置
        df['MA20'] = df['收盘价'].rolling(20).mean()
        df['MA50'] = df['收盘价'].rolling(50).mean()
        df['价格相对MA20'] = df['收盘价'] / df['MA20'] - 1
        df['价格相对MA50'] = df['收盘价'] / df['MA50'] - 1
        
        # 5. 计算波动率
        df['波动率_20'] = df['收盘价'].pct_change().rolling(20).std()
        
        # 6. 识别波段状态
        df = self._classify_wave_patterns(df)
        
        return df
    
    def _classify_wave_patterns(self, df: pd.DataFrame) -> pd.DataFrame:
        """分类波段模式"""
        df = df.copy()
        
        # 初始化波段状态
        df['波段状态'] = '震荡区域'
        df['波段强度'] = 0.0
        df['波段持续期'] = 0
        
        # 定义波段识别条件
        # 底部区域条件
        bottom_conditions = (
            (df['价格位置_50'] <= 0.3) &  # 价格在50周低位
            (df['RSI_14'] <= 35) &  # RSI超卖
            (df['价格相对MA50'] <= -0.1) &  # 价格低于MA50超过10%
            (df['趋势强度_20'] <= 0)  # 短期趋势向下或平缓
        )
        
        # 上升区域条件
        uptrend_conditions = (
            (df['价格位置_50'] > 0.3) & (df['价格位置_50'] <= 0.8) &  # 价格在中位
            (df['趋势强度_20'] > 0) &  # 短期趋势向上
            (df['价格相对MA20'] > 0) &  # 价格高于MA20
            (df['MACD'] > df['MACD_signal'])  # MACD金叉
        )
        
        # 顶部区域条件
        top_conditions = (
            (df['价格位置_50'] >= 0.8) &  # 价格在50周高位
            (df['RSI_14'] >= 65) &  # RSI超买
            (df['价格相对MA50'] >= 0.2) &  # 价格高于MA50超过20%
            (df['波动率_20'] > df['波动率_20'].rolling(50).mean())  # 波动率较高
        )
        
        # 下跌区域条件
        downtrend_conditions = (
            (df['价格位置_50'] > 0.2) & (df['价格位置_50'] < 0.8) &  # 价格在中位
            (df['趋势强度_20'] < 0) &  # 短期趋势向下
            (df['价格相对MA20'] < 0) &  # 价格低于MA20
            (df['MACD'] < df['MACD_signal'])  # MACD死叉
        )
        
        # 应用条件
        df.loc[bottom_conditions, '波段状态'] = '底部区域'
        df.loc[uptrend_conditions, '波段状态'] = '上升区域'
        df.loc[top_conditions, '波段状态'] = '顶部区域'
        df.loc[downtrend_conditions, '波段状态'] = '下跌区域'
        
        # 计算波段强度（0-1之间）
        df = self._calculate_wave_strength(df)
        
        # 计算波段持续期
        df = self._calculate_wave_duration(df)
        
        return df
    
    def _calculate_wave_strength(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算波段强度"""
        df = df.copy()
        
        for idx, row in df.iterrows():
            if row['波段状态'] == '底部区域':
                # 底部强度：RSI越低，价格位置越低，强度越高
                rsi_strength = max(0, (35 - row['RSI_14']) / 35) if not pd.isna(row['RSI_14']) else 0
                price_strength = max(0, (0.3 - row['价格位置_50'])) / 0.3 if not pd.isna(row['价格位置_50']) else 0
                df.loc[idx, '波段强度'] = (rsi_strength + price_strength) / 2
                
            elif row['波段状态'] == '上升区域':
                # 上升强度：趋势强度和MACD强度
                trend_strength = min(1, max(0, row['趋势强度_20'] * 1000)) if not pd.isna(row['趋势强度_20']) else 0
                macd_strength = min(1, max(0, (row['MACD'] - row['MACD_signal']) / abs(row['MACD_signal']))) if not pd.isna(row['MACD']) and row['MACD_signal'] != 0 else 0
                df.loc[idx, '波段强度'] = (trend_strength + macd_strength) / 2
                
            elif row['波段状态'] == '顶部区域':
                # 顶部强度：RSI越高，价格位置越高，强度越高
                rsi_strength = max(0, (row['RSI_14'] - 65) / 35) if not pd.isna(row['RSI_14']) else 0
                price_strength = max(0, (row['价格位置_50'] - 0.8)) / 0.2 if not pd.isna(row['价格位置_50']) else 0
                df.loc[idx, '波段强度'] = (rsi_strength + price_strength) / 2
                
            elif row['波段状态'] == '下跌区域':
                # 下跌强度：趋势向下的强度
                trend_strength = min(1, max(0, -row['趋势强度_20'] * 1000)) if not pd.isna(row['趋势强度_20']) else 0
                macd_strength = min(1, max(0, (row['MACD_signal'] - row['MACD']) / abs(row['MACD_signal']))) if not pd.isna(row['MACD']) and row['MACD_signal'] != 0 else 0
                df.loc[idx, '波段强度'] = (trend_strength + macd_strength) / 2
        
        return df
    
    def _calculate_wave_duration(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算波段持续期"""
        df = df.copy()
        df['波段持续期'] = 0
        
        current_pattern = None
        duration = 0
        
        for idx in df.index:
            pattern = df.loc[idx, '波段状态']
            
            if pattern == current_pattern:
                duration += 1
            else:
                duration = 1
                current_pattern = pattern
            
            df.loc[idx, '波段持续期'] = duration
        
        return df
    
    def analyze_wave_statistics(self, df: pd.DataFrame) -> Dict:
        """分析波段统计信息"""
        wave_stats = {}
        
        # 各波段状态的分布
        pattern_counts = df['波段状态'].value_counts()
        wave_stats['波段分布'] = pattern_counts.to_dict()
        
        # 各波段的平均强度
        avg_strength = df.groupby('波段状态')['波段强度'].mean()
        wave_stats['平均波段强度'] = avg_strength.to_dict()
        
        # 各波段的平均持续期
        avg_duration = df.groupby('波段状态')['波段持续期'].mean()
        wave_stats['平均持续期'] = avg_duration.to_dict()
        
        # 波段转换分析
        df['下一波段'] = df['波段状态'].shift(-1)
        transitions = df.groupby(['波段状态', '下一波段']).size().unstack(fill_value=0)
        wave_stats['波段转换矩阵'] = transitions.to_dict()
        
        self.pattern_statistics = wave_stats
        return wave_stats
    
    def visualize_wave_patterns(self, df: pd.DataFrame, stock_code: str = None) -> None:
        """可视化波段模式"""
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))
        
        # 设置颜色映射
        color_map = {
            '底部区域': 'green',
            '上升区域': 'blue', 
            '顶部区域': 'red',
            '下跌区域': 'orange',
            '震荡区域': 'gray'
        }
        
        # 1. 价格走势和波段标注
        axes[0].plot(df.index, df['收盘价'], linewidth=1, alpha=0.7, color='black')
        
        for pattern in color_map.keys():
            pattern_data = df[df['波段状态'] == pattern]
            if not pattern_data.empty:
                axes[0].scatter(pattern_data.index, pattern_data['收盘价'], 
                              c=color_map[pattern], label=pattern, alpha=0.6, s=10)
        
        axes[0].set_title(f'股票价格走势与波段识别 {stock_code or ""}', fontsize=14)
        axes[0].set_ylabel('收盘价')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 2. 波段强度
        for pattern in color_map.keys():
            pattern_data = df[df['波段状态'] == pattern]
            if not pattern_data.empty:
                axes[1].scatter(pattern_data.index, pattern_data['波段强度'], 
                              c=color_map[pattern], label=pattern, alpha=0.6, s=10)
        
        axes[1].set_title('波段强度分布', fontsize=14)
        axes[1].set_ylabel('波段强度')
        axes[1].set_ylim(0, 1)
        axes[1].grid(True, alpha=0.3)
        
        # 3. 技术指标
        axes[2].plot(df.index, df['RSI_14'], label='RSI_14', alpha=0.7)
        axes[2].axhline(y=30, color='green', linestyle='--', alpha=0.5, label='RSI超卖线')
        axes[2].axhline(y=70, color='red', linestyle='--', alpha=0.5, label='RSI超买线')
        axes[2].set_title('RSI指标', fontsize=14)
        axes[2].set_ylabel('RSI')
        axes[2].set_xlabel('时间')
        axes[2].legend()
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'波段分析_{stock_code or "示例"}.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_wave_summary_report(self, all_wave_data: pd.DataFrame) -> None:
        """创建波段分析汇总报告"""
        print("=== 波段分析汇总报告 ===")
        
        # 整体波段分布
        overall_distribution = all_wave_data['波段状态'].value_counts(normalize=True) * 100
        print(f"\n整体波段分布:")
        for pattern, percentage in overall_distribution.items():
            print(f"  {pattern}: {percentage:.2f}%")
        
        # 各波段的翻倍概率
        if 'is_double_20w' in all_wave_data.columns:
            double_prob_by_pattern = all_wave_data.groupby('波段状态')['is_double_20w'].mean() * 100
            print(f"\n各波段20周翻倍概率:")
            for pattern, prob in double_prob_by_pattern.items():
                print(f"  {pattern}: {prob:.2f}%")
        
        # 可视化波段分布
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 波段分布饼图
        axes[0, 0].pie(overall_distribution.values, labels=overall_distribution.index, autopct='%1.1f%%')
        axes[0, 0].set_title('波段状态分布')
        
        # 波段强度分布
        all_wave_data.boxplot(column='波段强度', by='波段状态', ax=axes[0, 1])
        axes[0, 1].set_title('各波段强度分布')
        axes[0, 1].set_xlabel('波段状态')
        
        # 波段持续期分布
        all_wave_data.boxplot(column='波段持续期', by='波段状态', ax=axes[1, 0])
        axes[1, 0].set_title('各波段持续期分布')
        axes[1, 0].set_xlabel('波段状态')
        
        # 翻倍概率对比
        if 'is_double_20w' in all_wave_data.columns:
            double_prob_by_pattern.plot(kind='bar', ax=axes[1, 1])
            axes[1, 1].set_title('各波段20周翻倍概率')
            axes[1, 1].set_ylabel('翻倍概率 (%)')
            axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig('波段分析汇总报告.png', dpi=300, bbox_inches='tight')
        plt.show()
