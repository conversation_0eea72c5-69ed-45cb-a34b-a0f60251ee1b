# 深度学习股票分析系统

基于Transformer架构的股票翻倍概率预测系统，提供完整的GUI界面和深度学习模型。

## 功能特点

### 🤖 深度学习模型
- **Transformer架构**: 使用先进的注意力机制处理时序数据
- **技术指标集成**: 自动计算MA、RSI、MACD、布林带等15个技术指标
- **类别平衡处理**: 使用加权损失函数处理数据不平衡问题
- **多周期预测**: 支持10周、20周、50周翻倍概率预测

### 📊 数据分析
- **自动特征工程**: 价格变化率、振幅、影线比例等
- **技术指标计算**: RSI、MACD、布林带、移动平均线
- **未来收益计算**: 自动计算不同时间窗口的收益率
- **数据质量分析**: 自动过滤无效数据，确保模型训练质量

### 🖥️ GUI界面
- **直观操作**: 拖拽式文件导入，滑块参数调节
- **实时进度**: 训练进度条和状态显示
- **多标签页**: 数据概览、推荐股票、模型性能分离显示
- **可视化图表**: matplotlib集成，支持中文显示

## 安装要求

### Python环境
```bash
Python 3.8+
```

### 依赖库
```bash
pip install -r requirements.txt
```

主要依赖：
- `torch`: 深度学习框架
- `PyQt5`: GUI界面
- `pandas`: 数据处理
- `matplotlib`: 数据可视化
- `scikit-learn`: 机器学习工具
- `ta`: 技术分析指标

## 使用方法

### 1. 启动GUI应用
```bash
python stock_gui.py
```

### 2. 导入数据
- 点击"选择CSV文件"按钮
- 选择股票数据文件（CSV格式）
- 数据格式要求：包含股票代码、股票名称、交易日期、开盘价、最高价、最低价、收盘价、成交量

### 3. 调整参数
- **序列长度**: 20-100，默认30（用于模型输入的历史数据长度）
- **预测周期**: 10周/20周/50周（预测翻倍的时间窗口）
- **训练轮次**: 10-200，默认50（模型训练的epoch数）

### 4. 开始分析
- 点击"开始分析"按钮
- 等待模型训练完成（进度条显示）
- 查看分析结果

### 5. 查看结果
- **数据概览**: 查看数据分布和技术指标统计
- **推荐股票**: 查看前20只推荐股票及其翻倍概率
- **模型性能**: 查看准确率、精确率、召回率、F1分数、AUC等指标

## 命令行使用

### 训练模型
```bash
python train_model.py
```

### 单独数据分析
```bash
python data_analyzer.py
```

## 文件结构

```
stock_1/
├── stock_gui.py              # GUI主程序
├── train_model.py            # 命令行训练脚本
├── stock_transformer.py      # Transformer模型定义
├── data_analyzer.py          # 数据分析模块
├── requirements.txt          # 依赖库列表
├── README.md                 # 说明文档
├── 缺口股票时序数据集_*.csv   # 示例数据文件
├── stock_recommendations.csv # 推荐结果
├── all_predictions.csv       # 所有预测结果
└── best_stock_model.pth      # 训练好的模型文件
```

## 数据格式

### 输入CSV格式
```csv
股票代码,股票名称,交易日期,开盘价,最高价,最低价,收盘价,成交量
000001.SZ,平安银行,2023-01-01,10.50,10.80,10.30,10.70,1000000
...
```

### 输出结果格式
推荐股票包含以下字段：
- 排名
- 股票代码
- 股票名称  
- 当前价格
- 翻倍概率（%）
- 最新日期

## 模型架构

### Transformer组件
- **位置编码**: 为时序数据添加位置信息
- **多头注意力**: 8个注意力头，捕捉不同时间尺度的模式
- **前馈网络**: 4倍隐藏层维度的全连接网络
- **层归一化**: 稳定训练过程
- **Dropout**: 防止过拟合

### 特征工程
- 价格相关：价格变化率、振幅、影线比例
- 技术指标：MA5/10/20/50比率、RSI、MACD、布林带位置
- 成交量：成交量比率
- 位置指标：52周价格位置

## 性能指标

### 模型评估
- **准确率**: 整体预测正确率
- **精确率**: 预测为正例中实际为正例的比例
- **召回率**: 实际正例中被正确预测的比例
- **F1分数**: 精确率和召回率的调和平均
- **AUC**: ROC曲线下面积，衡量分类性能

### 典型性能
基于示例数据集的性能：
- 准确率: ~69%
- 精确率: ~28%
- 召回率: ~49%
- F1分数: ~35%
- AUC: ~67%

## 风险提示

⚠️ **重要声明**
1. 本系统预测结果仅供参考，不构成投资建议
2. 股票投资存在风险，请谨慎决策
3. 模型基于历史数据训练，未来表现可能不同
4. 建议结合其他分析方法综合判断
5. 投资前请咨询专业的金融顾问

## 技术支持

### 常见问题
1. **CUDA支持**: 如果有NVIDIA GPU，模型会自动使用CUDA加速
2. **内存要求**: 建议8GB以上内存，大数据集可能需要更多
3. **中文字体**: 系统会自动配置中文字体显示

### 故障排除
- 如果GUI无法启动，检查PyQt5是否正确安装
- 如果训练过程中内存不足，减少batch_size或序列长度
- 如果预测结果异常，检查数据格式是否正确

## 更新日志

### v1.0 (2025-06-25)
- 初始版本发布
- 实现Transformer模型
- 完成GUI界面
- 支持多周期预测
- 集成技术指标计算

## 许可证

本项目仅供学习和研究使用。

---

**开发者**: AI Assistant  
**版本**: 1.0  
**更新时间**: 2025-06-25
