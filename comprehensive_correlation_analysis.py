"""
综合特征相关性分析
结合波段信息进行深度相关性分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import mutual_info_regression, SelectKBest, f_regression
from sklearn.ensemble import RandomForestRegressor
from scipy.stats import pearsonr, spearmanr
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['mathtext.fontset'] = 'stix'

class ComprehensiveCorrelationAnalyzer:
    """综合相关性分析器"""
    
    def __init__(self):
        self.feature_importance = None
        self.correlation_matrix = None
        self.wave_specific_correlations = None
    
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("正在加载数据...")
        
        # 读取波段分析结果
        try:
            wave_data = pd.read_csv('波段分析结果.csv')
            print(f"成功读取波段分析数据: {len(wave_data)} 行")
        except FileNotFoundError:
            print("未找到波段分析结果，请先运行波段分析")
            return None
        
        # 选择特征列
        feature_columns = [
            # 价格特征
            '价格变化率', '振幅', '上影线比例', '下影线比例',
            
            # 移动平均线特征
            'MA5_ratio', 'MA10_ratio', 'MA20_ratio', 'MA50_ratio', 'MA100_ratio',
            
            # 成交量特征
            '成交量比率_5', '成交量比率_20',
            
            # 技术指标
            'RSI_14', 'RSI_6', 'MACD', 'MACD_signal', 'MACD_histogram',
            'BB_position', 'BB_width', 'K', 'D', 'J', 'WR', 'CCI', 'ATR_ratio', 'ADX', 'OBV_ratio',
            
            # 价格位置指标
            '价格位置_20周', '价格位置_50周', '价格位置_100周',
            
            # 趋势指标
            '趋势强度_20', '趋势强度_50',
            
            # 波段特征
            '波段强度', '波段持续期', '价格百分位_20', '价格百分位_50'
        ]
        
        # 添加波段状态的数值编码
        wave_mapping = {'底部区域': 1, '上升区域': 2, '震荡区域': 3, '下跌区域': 4, '顶部区域': 5}
        wave_data['波段状态_编码'] = wave_data['波段状态'].map(wave_mapping)
        feature_columns.append('波段状态_编码')
        
        # 目标变量
        target_columns = ['is_double_10w', 'is_double_20w', 'is_double_30w', 'is_double_50w']
        
        # 过滤有效数据
        valid_columns = [col for col in feature_columns if col in wave_data.columns]
        valid_targets = [col for col in target_columns if col in wave_data.columns]
        
        if not valid_targets:
            print("未找到目标变量，添加翻倍标签...")
            for period in [10, 20, 30, 50]:
                if f'is_double_{period}w' not in wave_data.columns:
                    wave_data[f'future_return_{period}w'] = wave_data.groupby('股票代码')['收盘价'].shift(-period) / wave_data['收盘价'] - 1
                    wave_data[f'is_double_{period}w'] = (wave_data[f'future_return_{period}w'] >= 1.0).astype(int)
            valid_targets = [f'is_double_{period}w' for period in [10, 20, 30, 50]]
        
        print(f"有效特征数量: {len(valid_columns)}")
        print(f"有效目标变量: {len(valid_targets)}")
        
        return wave_data, valid_columns, valid_targets
    
    def calculate_comprehensive_correlations(self, data, feature_columns, target_columns):
        """计算综合相关性"""
        print("正在计算综合相关性...")
        
        correlation_results = []
        
        for target in target_columns:
            print(f"分析目标变量: {target}")
            
            # 准备数据
            analysis_data = data[feature_columns + [target]].dropna()
            
            if len(analysis_data) < 50:
                print(f"  {target} 有效数据不足，跳过")
                continue
            
            X = analysis_data[feature_columns]
            y = analysis_data[target]
            
            # 1. 皮尔逊相关系数
            pearson_corrs = {}
            for feature in feature_columns:
                try:
                    corr, p_value = pearsonr(X[feature].fillna(0), y)
                    pearson_corrs[feature] = {'correlation': corr, 'p_value': p_value}
                except:
                    pearson_corrs[feature] = {'correlation': 0, 'p_value': 1}
            
            # 2. 斯皮尔曼相关系数
            spearman_corrs = {}
            for feature in feature_columns:
                try:
                    corr, p_value = spearmanr(X[feature].fillna(0), y)
                    spearman_corrs[feature] = {'correlation': corr, 'p_value': p_value}
                except:
                    spearman_corrs[feature] = {'correlation': 0, 'p_value': 1}
            
            # 3. 互信息
            try:
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X.fillna(0))
                mi_scores = mutual_info_regression(X_scaled, y, random_state=42)
                mi_dict = dict(zip(feature_columns, mi_scores))
            except:
                mi_dict = {feature: 0 for feature in feature_columns}
            
            # 4. F统计量
            try:
                f_scores, f_pvalues = f_regression(X.fillna(0), y)
                f_dict = dict(zip(feature_columns, f_scores))
                f_pvalue_dict = dict(zip(feature_columns, f_pvalues))
            except:
                f_dict = {feature: 0 for feature in feature_columns}
                f_pvalue_dict = {feature: 1 for feature in feature_columns}
            
            # 5. 随机森林特征重要性
            try:
                rf = RandomForestRegressor(n_estimators=100, random_state=42)
                rf.fit(X.fillna(0), y)
                rf_importance = dict(zip(feature_columns, rf.feature_importances_))
            except:
                rf_importance = {feature: 0 for feature in feature_columns}
            
            # 合并结果
            for feature in feature_columns:
                correlation_results.append({
                    '特征名称': feature,
                    '目标变量': target,
                    '皮尔逊相关系数': pearson_corrs[feature]['correlation'],
                    '皮尔逊P值': pearson_corrs[feature]['p_value'],
                    '斯皮尔曼相关系数': spearman_corrs[feature]['correlation'],
                    '斯皮尔曼P值': spearman_corrs[feature]['p_value'],
                    '互信息得分': mi_dict[feature],
                    'F统计量': f_dict[feature],
                    'F统计P值': f_pvalue_dict[feature],
                    '随机森林重要性': rf_importance[feature],
                    '绝对皮尔逊相关系数': abs(pearson_corrs[feature]['correlation'])
                })
        
        return pd.DataFrame(correlation_results)
    
    def analyze_wave_specific_correlations(self, data, feature_columns, target_columns):
        """分析各波段的特定相关性"""
        print("正在分析各波段的特定相关性...")
        
        wave_correlations = {}
        
        for wave_pattern in data['波段状态'].unique():
            print(f"分析波段: {wave_pattern}")
            wave_data = data[data['波段状态'] == wave_pattern]
            
            if len(wave_data) < 30:
                continue
            
            pattern_correlations = []
            
            for target in target_columns:
                if target not in wave_data.columns:
                    continue
                
                analysis_data = wave_data[feature_columns + [target]].dropna()
                
                if len(analysis_data) < 20:
                    continue
                
                X = analysis_data[feature_columns]
                y = analysis_data[target]
                
                for feature in feature_columns:
                    try:
                        corr, p_value = pearsonr(X[feature].fillna(0), y)
                        pattern_correlations.append({
                            '波段状态': wave_pattern,
                            '特征名称': feature,
                            '目标变量': target,
                            '相关系数': corr,
                            'P值': p_value,
                            '绝对相关系数': abs(corr),
                            '样本数': len(analysis_data)
                        })
                    except:
                        continue
            
            if pattern_correlations:
                wave_correlations[wave_pattern] = pd.DataFrame(pattern_correlations)
        
        self.wave_specific_correlations = wave_correlations
        return wave_correlations
    
    def generate_feature_importance_ranking(self, correlation_df):
        """生成特征重要性排序"""
        print("正在生成特征重要性排序...")
        
        # 计算综合重要性得分
        feature_scores = []
        
        for feature in correlation_df['特征名称'].unique():
            feature_data = correlation_df[correlation_df['特征名称'] == feature]
            
            # 各种指标的平均值
            avg_abs_pearson = feature_data['绝对皮尔逊相关系数'].mean()
            avg_abs_spearman = abs(feature_data['斯皮尔曼相关系数']).mean()
            avg_mi = feature_data['互信息得分'].mean()
            avg_f_stat = feature_data['F统计量'].mean()
            avg_rf_importance = feature_data['随机森林重要性'].mean()
            
            # 最大值
            max_abs_pearson = feature_data['绝对皮尔逊相关系数'].max()
            max_mi = feature_data['互信息得分'].max()
            
            # 显著性（P值小于0.05的比例）
            significant_ratio = (feature_data['皮尔逊P值'] < 0.05).mean()
            
            # 综合得分（可调整权重）
            composite_score = (
                0.25 * avg_abs_pearson +
                0.15 * avg_abs_spearman +
                0.20 * avg_mi +
                0.15 * avg_rf_importance +
                0.15 * max_abs_pearson +
                0.10 * significant_ratio
            )
            
            feature_scores.append({
                '特征名称': feature,
                '平均绝对皮尔逊相关系数': avg_abs_pearson,
                '平均绝对斯皮尔曼相关系数': avg_abs_spearman,
                '平均互信息得分': avg_mi,
                '平均F统计量': avg_f_stat,
                '平均随机森林重要性': avg_rf_importance,
                '最大绝对相关系数': max_abs_pearson,
                '最大互信息得分': max_mi,
                '显著性比例': significant_ratio,
                '综合重要性得分': composite_score
            })
        
        importance_df = pd.DataFrame(feature_scores)
        importance_df = importance_df.sort_values('综合重要性得分', ascending=False)
        
        self.feature_importance = importance_df
        return importance_df
    
    def create_comprehensive_visualizations(self, correlation_df, importance_df):
        """创建综合可视化"""
        print("正在创建可视化图表...")
        
        # 创建多子图
        fig = plt.figure(figsize=(20, 15))
        
        # 1. 特征重要性排序 (前20名)
        ax1 = plt.subplot(2, 3, 1)
        top_features = importance_df.head(20)
        y_pos = np.arange(len(top_features))
        ax1.barh(y_pos, top_features['综合重要性得分'], alpha=0.7)
        ax1.set_yticks(y_pos)
        ax1.set_yticklabels(top_features['特征名称'], fontsize=8)
        ax1.set_xlabel('综合重要性得分')
        ax1.set_title('特征重要性排序 (前20名)', fontweight='bold')
        ax1.invert_yaxis()
        
        # 2. 相关性热力图
        ax2 = plt.subplot(2, 3, 2)
        pivot_data = correlation_df.pivot(index='特征名称', columns='目标变量', values='皮尔逊相关系数')
        top_features_list = importance_df.head(15)['特征名称'].tolist()
        pivot_subset = pivot_data.loc[pivot_data.index.intersection(top_features_list)]
        
        sns.heatmap(pivot_subset, annot=True, cmap='RdBu_r', center=0, fmt='.3f', ax=ax2)
        ax2.set_title('前15重要特征相关性热力图', fontweight='bold')
        ax2.set_xlabel('翻倍周期')
        ax2.set_ylabel('技术指标')
        
        # 3. 不同评估方法的对比
        ax3 = plt.subplot(2, 3, 3)
        top_10_features = importance_df.head(10)['特征名称']
        methods = ['平均绝对皮尔逊相关系数', '平均互信息得分', '平均随机森林重要性']
        
        x = np.arange(len(top_10_features))
        width = 0.25
        
        for i, method in enumerate(methods):
            values = [importance_df[importance_df['特征名称'] == feature][method].iloc[0] 
                     for feature in top_10_features]
            ax3.bar(x + i*width, values, width, label=method, alpha=0.7)
        
        ax3.set_xlabel('特征')
        ax3.set_ylabel('得分')
        ax3.set_title('不同评估方法对比 (前10特征)', fontweight='bold')
        ax3.set_xticks(x + width)
        ax3.set_xticklabels(top_10_features, rotation=45, ha='right', fontsize=8)
        ax3.legend()
        
        # 4. 显著性分析
        ax4 = plt.subplot(2, 3, 4)
        significance_data = importance_df.head(15)
        ax4.scatter(significance_data['平均绝对皮尔逊相关系数'], 
                   significance_data['显著性比例'], alpha=0.7)
        
        for i, txt in enumerate(significance_data['特征名称']):
            ax4.annotate(txt, 
                        (significance_data['平均绝对皮尔逊相关系数'].iloc[i], 
                         significance_data['显著性比例'].iloc[i]),
                        fontsize=6, rotation=45)
        
        ax4.set_xlabel('平均绝对相关系数')
        ax4.set_ylabel('显著性比例')
        ax4.set_title('相关性 vs 显著性', fontweight='bold')
        ax4.grid(True, alpha=0.3)
        
        # 5. 各目标变量的最佳特征
        ax5 = plt.subplot(2, 3, 5)
        target_best_features = {}
        for target in correlation_df['目标变量'].unique():
            target_data = correlation_df[correlation_df['目标变量'] == target]
            best_feature = target_data.loc[target_data['绝对皮尔逊相关系数'].idxmax()]
            target_best_features[target] = {
                'feature': best_feature['特征名称'],
                'correlation': best_feature['绝对皮尔逊相关系数']
            }
        
        targets = list(target_best_features.keys())
        correlations = [target_best_features[t]['correlation'] for t in targets]
        
        ax5.bar(targets, correlations, alpha=0.7)
        ax5.set_ylabel('最高绝对相关系数')
        ax5.set_title('各翻倍周期的最佳预测特征', fontweight='bold')
        ax5.tick_params(axis='x', rotation=45)
        
        # 添加特征名称标注
        for i, (target, corr) in enumerate(zip(targets, correlations)):
            feature_name = target_best_features[target]['feature']
            ax5.text(i, corr + 0.01, feature_name, ha='center', va='bottom', 
                    fontsize=8, rotation=45)
        
        # 6. 综合得分分布
        ax6 = plt.subplot(2, 3, 6)
        ax6.hist(importance_df['综合重要性得分'], bins=20, alpha=0.7, edgecolor='black')
        ax6.set_xlabel('综合重要性得分')
        ax6.set_ylabel('特征数量')
        ax6.set_title('特征重要性得分分布', fontweight='bold')
        ax6.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('综合特征相关性分析.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    print("=== 开始综合特征相关性分析 ===")
    
    analyzer = ComprehensiveCorrelationAnalyzer()
    
    # 加载数据
    data, feature_columns, target_columns = analyzer.load_and_prepare_data()
    
    if data is None:
        return
    
    # 计算综合相关性
    correlation_df = analyzer.calculate_comprehensive_correlations(data, feature_columns, target_columns)
    
    # 分析各波段的特定相关性
    wave_correlations = analyzer.analyze_wave_specific_correlations(data, feature_columns, target_columns)
    
    # 生成特征重要性排序
    importance_df = analyzer.generate_feature_importance_ranking(correlation_df)
    
    # 保存结果
    correlation_df.to_csv('综合特征相关性分析.csv', index=False, encoding='utf-8-sig')
    importance_df.to_csv('最终特征重要性排序.csv', index=False, encoding='utf-8-sig')
    
    # 保存各波段的相关性分析
    for wave_pattern, wave_df in wave_correlations.items():
        filename = f'波段相关性_{wave_pattern}.csv'
        wave_df.to_csv(filename, index=False, encoding='utf-8-sig')
    
    # 创建可视化
    analyzer.create_comprehensive_visualizations(correlation_df, importance_df)
    
    # 打印结果摘要
    print("\n=== 特征重要性排序 (前15名) ===")
    print(importance_df[['特征名称', '综合重要性得分', '平均绝对皮尔逊相关系数', '显著性比例']].head(15).to_string(index=False))
    
    print(f"\n=== 分析完成 ===")
    print("生成的文件:")
    print("- 综合特征相关性分析.csv: 完整的相关性分析结果")
    print("- 最终特征重要性排序.csv: 特征重要性排序")
    print("- 波段相关性_*.csv: 各波段的特定相关性分析")
    print("- 综合特征相关性分析.png: 综合可视化图表")

if __name__ == "__main__":
    main()
