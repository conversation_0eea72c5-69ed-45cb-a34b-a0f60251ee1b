"""
深度学习股票分析系统演示脚本
展示系统的主要功能和使用方法
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from data_analyzer import StockDataAnalyzer
from stock_transformer import StockPredictor
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['mathtext.fontset'] = 'stix'

def demo_data_analysis():
    """演示数据分析功能"""
    print("=" * 60)
    print("📊 数据分析演示")
    print("=" * 60)
    
    # 加载数据
    print("1. 加载股票数据...")
    analyzer = StockDataAnalyzer('缺口股票时序数据集_20250625_213940.csv')
    data = analyzer.process_all_stocks()
    
    print(f"✅ 数据加载完成")
    print(f"   - 总记录数: {len(data):,}")
    print(f"   - 股票数量: {data['股票代码'].nunique()}")
    print(f"   - 时间范围: {data['交易日期'].min()} 到 {data['交易日期'].max()}")
    
    # 分析翻倍概率
    print("\n2. 翻倍概率分析...")
    for weeks in [10, 20, 50]:
        col = f'double_{weeks}w'
        if col in data.columns:
            rate = data[col].mean()
            count = data[col].sum()
            print(f"   - {weeks}周翻倍: {rate:.3f} ({rate*100:.1f}%) - {count}只股票")
    
    # 技术指标统计
    print("\n3. 技术指标统计...")
    if 'RSI' in data.columns:
        rsi_mean = data['RSI'].mean()
        rsi_oversold = (data['RSI'] < 30).sum()
        rsi_overbought = (data['RSI'] > 70).sum()
        print(f"   - RSI平均值: {rsi_mean:.2f}")
        print(f"   - 超卖状态(<30): {rsi_oversold}条记录")
        print(f"   - 超买状态(>70): {rsi_overbought}条记录")
    
    return data

def demo_model_training():
    """演示模型训练功能"""
    print("\n" + "=" * 60)
    print("🤖 模型训练演示")
    print("=" * 60)
    
    # 加载数据
    analyzer = StockDataAnalyzer('缺口股票时序数据集_20250625_213940.csv')
    data = analyzer.process_all_stocks()
    
    print("1. 创建Transformer模型...")
    predictor = StockPredictor(sequence_length=30, prediction_weeks=50)
    
    print("2. 开始训练模型...")
    print("   (使用较少的轮次进行快速演示)")
    
    metrics = predictor.train(
        data, 
        test_size=0.2, 
        batch_size=64, 
        epochs=20,  # 演示用较少轮次
        learning_rate=0.0005
    )
    
    print("\n✅ 模型训练完成")
    print(f"   - 准确率: {metrics['accuracy']:.4f}")
    print(f"   - 精确率: {metrics['precision']:.4f}")
    print(f"   - 召回率: {metrics['recall']:.4f}")
    print(f"   - F1分数: {metrics['f1']:.4f}")
    print(f"   - AUC值: {metrics['auc']:.4f}")
    
    return predictor, data

def demo_stock_prediction(predictor, data):
    """演示股票预测功能"""
    print("\n" + "=" * 60)
    print("📈 股票预测演示")
    print("=" * 60)
    
    print("1. 生成股票预测...")
    predictions = predictor.predict(data)
    
    print(f"✅ 预测完成，共预测 {len(predictions)} 只股票")
    
    print("\n2. 获取推荐股票...")
    recommendations = predictor.get_top_recommendations(predictions, top_n=10)
    
    print("\n🏆 前10只推荐股票:")
    print("-" * 80)
    print(f"{'排名':<4} {'股票代码':<12} {'股票名称':<10} {'当前价格':<10} {'翻倍概率':<10}")
    print("-" * 80)
    
    for rec in recommendations:
        print(f"{rec['rank']:<4} {rec['stock_code']:<12} {rec['stock_name']:<10} "
              f"{rec['current_price']:<10.2f} {rec['probability_percent']:<10.2f}%")
    
    return recommendations

def demo_visualization(data, recommendations):
    """演示可视化功能"""
    print("\n" + "=" * 60)
    print("📊 可视化演示")
    print("=" * 60)
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('深度学习股票分析系统 - 数据可视化', fontsize=16, fontweight='bold')
    
    # 1. 翻倍概率分布
    ax1 = axes[0, 0]
    weeks = [10, 20, 50]
    probs = []
    for w in weeks:
        col = f'double_{w}w'
        if col in data.columns:
            probs.append(data[col].mean() * 100)
        else:
            probs.append(0)
    
    bars1 = ax1.bar(weeks, probs, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
    ax1.set_title('不同周期翻倍概率分布')
    ax1.set_xlabel('周数')
    ax1.set_ylabel('概率 (%)')
    
    # 添加数值标签
    for bar, prob in zip(bars1, probs):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                f'{prob:.1f}%', ha='center', va='bottom')
    
    # 2. 推荐股票概率
    ax2 = axes[0, 1]
    if recommendations:
        names = [rec['stock_name'][:4] for rec in recommendations[:8]]
        probs = [rec['probability_percent'] for rec in recommendations[:8]]
        
        bars2 = ax2.barh(range(len(names)), probs, color='#45B7D1')
        ax2.set_yticks(range(len(names)))
        ax2.set_yticklabels(names)
        ax2.set_xlabel('翻倍概率 (%)')
        ax2.set_title('前8只推荐股票')
        
        # 添加数值标签
        for i, (bar, prob) in enumerate(zip(bars2, probs)):
            ax2.text(bar.get_width() + 0.5, bar.get_y() + bar.get_height()/2, 
                    f'{prob:.1f}%', va='center', fontsize=9)
    
    # 3. RSI分布
    ax3 = axes[1, 0]
    if 'RSI' in data.columns:
        ax3.hist(data['RSI'].dropna(), bins=30, alpha=0.7, color='#FFEAA7', edgecolor='black')
        ax3.axvline(30, color='red', linestyle='--', alpha=0.7, label='超卖线')
        ax3.axvline(70, color='red', linestyle='--', alpha=0.7, label='超买线')
        ax3.set_title('RSI指标分布')
        ax3.set_xlabel('RSI值')
        ax3.set_ylabel('频次')
        ax3.legend()
    
    # 4. 价格变化率分布
    ax4 = axes[1, 1]
    if '价格变化率' in data.columns:
        ax4.hist(data['价格变化率'].dropna(), bins=50, alpha=0.7, color='#96CEB4', edgecolor='black')
        ax4.set_title('价格变化率分布')
        ax4.set_xlabel('变化率')
        ax4.set_ylabel('频次')
        ax4.axvline(0, color='red', linestyle='-', alpha=0.7, label='零线')
        ax4.legend()
    
    plt.tight_layout()
    plt.savefig('demo_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 可视化图表已生成并保存为 demo_visualization.png")

def demo_risk_analysis(recommendations):
    """演示风险分析"""
    print("\n" + "=" * 60)
    print("⚠️  风险分析演示")
    print("=" * 60)
    
    if not recommendations:
        print("❌ 无推荐数据进行风险分析")
        return
    
    # 概率分布分析
    probs = [rec['probability_percent'] for rec in recommendations]
    prob_mean = np.mean(probs)
    prob_std = np.std(probs)
    
    print("1. 推荐股票概率分析:")
    print(f"   - 平均翻倍概率: {prob_mean:.2f}%")
    print(f"   - 概率标准差: {prob_std:.2f}%")
    print(f"   - 最高概率: {max(probs):.2f}%")
    print(f"   - 最低概率: {min(probs):.2f}%")
    
    # 价格分析
    prices = [rec['current_price'] for rec in recommendations]
    price_mean = np.mean(prices)
    
    print(f"\n2. 推荐股票价格分析:")
    print(f"   - 平均价格: {price_mean:.2f}元")
    print(f"   - 价格范围: {min(prices):.2f} - {max(prices):.2f}元")
    
    # 风险提示
    print(f"\n3. 风险提示:")
    print("   ⚠️  模型预测存在不确定性")
    print("   ⚠️  历史表现不代表未来收益")
    print("   ⚠️  建议分散投资，控制仓位")
    print("   ⚠️  请结合基本面分析")
    print("   ⚠️  设置止损点，控制风险")

def main():
    """主演示函数"""
    print("🚀 深度学习股票分析系统演示")
    print("=" * 60)
    print("本演示将展示系统的主要功能:")
    print("1. 数据分析和处理")
    print("2. 深度学习模型训练")
    print("3. 股票预测和推荐")
    print("4. 数据可视化")
    print("5. 风险分析")
    print("=" * 60)
    
    try:
        # 1. 数据分析演示
        data = demo_data_analysis()
        
        # 2. 模型训练演示
        predictor, data = demo_model_training()
        
        # 3. 股票预测演示
        recommendations = demo_stock_prediction(predictor, data)
        
        # 4. 可视化演示
        demo_visualization(data, recommendations)
        
        # 5. 风险分析演示
        demo_risk_analysis(recommendations)
        
        print("\n" + "=" * 60)
        print("🎉 演示完成！")
        print("=" * 60)
        print("📁 生成的文件:")
        print("   - demo_visualization.png (可视化图表)")
        print("   - best_stock_model.pth (训练好的模型)")
        print("\n💡 下一步:")
        print("   - 运行 python stock_gui.py 启动GUI界面")
        print("   - 查看 README.md 了解详细使用说明")
        print("   - 查看生成的CSV文件获取完整预测结果")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        print("请检查数据文件是否存在，以及依赖库是否正确安装")

if __name__ == "__main__":
    main()
