"""
运行翻倍行情识别分析
"""

from enhanced_stock_analyzer import EnhancedStockAnalyzer
import pandas as pd
import numpy as np

def main():
    # 初始化分析器
    analyzer = EnhancedStockAnalyzer('缺口股票时序数据集_20250625_213940.csv')
    
    # 加载数据
    raw_data = analyzer.load_and_preprocess_data()
    
    # 识别翻倍机会
    double_analysis = analyzer.identify_double_opportunities([10, 20, 30, 50])
    
    # 可视化分析结果
    analyzer.visualize_double_analysis()
    
    # 保存详细的翻倍股票统计
    double_stats = double_analysis['股票统计']
    
    # 筛选出有翻倍机会的股票
    high_potential_stocks = double_stats[
        (double_stats['20周翻倍次数'] > 0) | 
        (double_stats['30周翻倍次数'] > 0) | 
        (double_stats['50周翻倍次数'] > 0)
    ].copy()
    
    # 按20周翻倍概率排序
    high_potential_stocks = high_potential_stocks.sort_values('20周翻倍概率', ascending=False)
    
    print(f"\n=== 有翻倍潜力的股票数量: {len(high_potential_stocks)} ===")
    print("\n前20只翻倍潜力股票:")
    print(high_potential_stocks[['股票代码', '股票名称', '20周翻倍次数', '20周最大涨幅', '20周翻倍概率']].head(20).to_string(index=False))
    
    # 保存结果
    double_stats.to_csv('翻倍机会统计.csv', index=False, encoding='utf-8-sig')
    high_potential_stocks.to_csv('高翻倍潜力股票.csv', index=False, encoding='utf-8-sig')
    
    # 分析翻倍机会的时间分布
    print("\n=== 开始分析翻倍机会的时间分布 ===")
    
    # 选择几只有代表性的翻倍股票进行详细分析
    top_stocks = high_potential_stocks.head(5)['股票代码'].tolist()
    
    time_analysis = []
    for stock_code in top_stocks:
        stock_data = raw_data[raw_data['股票代码'] == stock_code].copy()
        stock_data = stock_data.sort_values('交易日期')
        
        # 计算20周后的收益率
        stock_data['future_return_20w'] = stock_data['收盘价'].shift(-20) / stock_data['收盘价'] - 1
        stock_data['is_double_20w'] = (stock_data['future_return_20w'] >= 1.0)
        
        # 找出翻倍机会的时间点
        double_opportunities = stock_data[stock_data['is_double_20w'] == True]
        
        for _, row in double_opportunities.iterrows():
            time_analysis.append({
                '股票代码': stock_code,
                '股票名称': row['股票名称'],
                '翻倍起始日期': row['交易日期'],
                '起始价格': row['收盘价'],
                '20周后收益率': row['future_return_20w'] * 100
            })
    
    if time_analysis:
        time_df = pd.DataFrame(time_analysis)
        time_df.to_csv('翻倍机会时间分析.csv', index=False, encoding='utf-8-sig')
        
        print(f"\n翻倍机会时间分析 (前5只股票):")
        print(time_df.to_string(index=False))
        
        # 分析翻倍机会的季节性分布
        time_df['翻倍起始日期'] = pd.to_datetime(time_df['翻倍起始日期'])
        time_df['月份'] = time_df['翻倍起始日期'].dt.month
        time_df['季度'] = time_df['翻倍起始日期'].dt.quarter
        
        print(f"\n翻倍机会的月份分布:")
        month_dist = time_df['月份'].value_counts().sort_index()
        print(month_dist)
        
        print(f"\n翻倍机会的季度分布:")
        quarter_dist = time_df['季度'].value_counts().sort_index()
        print(quarter_dist)
    
    print("\n=== 翻倍行情识别分析完成 ===")
    print("生成的文件:")
    print("- 翻倍机会统计.csv: 所有股票的翻倍统计信息")
    print("- 高翻倍潜力股票.csv: 筛选出的有翻倍潜力的股票")
    print("- 翻倍机会时间分析.csv: 翻倍机会的时间分布分析")
    print("- 翻倍机会分析.png: 可视化分析图表")

if __name__ == "__main__":
    main()
