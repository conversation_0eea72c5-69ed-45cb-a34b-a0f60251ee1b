"""
优化版股票预测模型训练脚本
包含性能对比和详细分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from data_analyzer import StockDataAnalyzer
from stock_transformer import StockPredictor
from optimized_transformer import OptimizedStockPredictor
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['mathtext.fontset'] = 'stix'

def compare_models():
    """对比原始模型和优化模型"""
    print("=" * 80)
    print("🚀 深度学习股票分析系统 - 模型性能优化对比")
    print("=" * 80)
    
    # 加载数据
    print("1. 加载和处理数据...")
    analyzer = StockDataAnalyzer('缺口股票时序数据集_20250625_213940.csv')
    data = analyzer.process_all_stocks()
    
    print(f"✅ 数据处理完成")
    print(f"   - 总记录数: {len(data):,}")
    print(f"   - 股票数量: {data['股票代码'].nunique()}")
    print(f"   - 时间范围: {data['交易日期'].min()} 到 {data['交易日期'].max()}")
    
    # 训练原始模型
    print("\n" + "=" * 60)
    print("📊 训练原始模型...")
    print("=" * 60)
    
    original_predictor = StockPredictor(sequence_length=30, prediction_weeks=50)
    original_metrics = original_predictor.train(
        data, 
        test_size=0.2, 
        batch_size=64, 
        epochs=50, 
        learning_rate=0.0005
    )
    
    # 训练优化模型
    print("\n" + "=" * 60)
    print("🔧 训练优化模型...")
    print("=" * 60)
    
    optimized_predictor = OptimizedStockPredictor(sequence_length=40, prediction_weeks=50)
    optimized_metrics = optimized_predictor.train_with_kfold(
        data, 
        n_splits=3, 
        batch_size=32, 
        epochs=100, 
        learning_rate=0.0003
    )
    
    # 性能对比
    print("\n" + "=" * 80)
    print("📈 模型性能对比结果")
    print("=" * 80)
    
    metrics_names = ['accuracy', 'precision', 'recall', 'f1', 'auc']
    metrics_chinese = ['准确率', '精确率', '召回率', 'F1分数', 'AUC值']
    
    print(f"{'指标':<10} {'原始模型':<15} {'优化模型':<15} {'提升幅度':<15}")
    print("-" * 65)
    
    improvements = {}
    for metric, chinese in zip(metrics_names, metrics_chinese):
        original_val = original_metrics[metric]
        optimized_val = optimized_metrics[metric]
        improvement = ((optimized_val - original_val) / original_val) * 100
        improvements[metric] = improvement
        
        print(f"{chinese:<10} {original_val:<15.4f} {optimized_val:<15.4f} {improvement:<15.2f}%")
    
    # 生成预测对比
    print("\n" + "=" * 60)
    print("🎯 生成预测结果对比...")
    print("=" * 60)
    
    # 原始模型预测
    original_predictions = original_predictor.predict(data)
    original_recommendations = original_predictor.get_top_recommendations(original_predictions, top_n=10)
    
    # 优化模型预测
    optimized_predictions = optimized_predictor.predict(data)
    optimized_recommendations = optimized_predictor.get_top_recommendations(optimized_predictions, top_n=10)
    
    # 显示推荐对比
    print("\n📊 前10只推荐股票对比:")
    print("\n原始模型推荐:")
    print("-" * 70)
    print(f"{'排名':<4} {'股票代码':<12} {'股票名称':<10} {'翻倍概率':<10}")
    print("-" * 70)
    for rec in original_recommendations:
        print(f"{rec['rank']:<4} {rec['stock_code']:<12} {rec['stock_name']:<10} {rec['probability_percent']:<10.2f}%")
    
    print("\n优化模型推荐:")
    print("-" * 70)
    print(f"{'排名':<4} {'股票代码':<12} {'股票名称':<10} {'翻倍概率':<10}")
    print("-" * 70)
    for rec in optimized_recommendations:
        print(f"{rec['rank']:<4} {rec['stock_code']:<12} {rec['stock_name']:<10} {rec['probability_percent']:<10.2f}%")
    
    # 保存结果
    print("\n" + "=" * 60)
    print("💾 保存对比结果...")
    print("=" * 60)
    
    # 保存原始模型结果
    original_df = pd.DataFrame(original_recommendations)
    original_df.to_csv('original_model_recommendations.csv', index=False, encoding='utf-8-sig')
    
    # 保存优化模型结果
    optimized_df = pd.DataFrame(optimized_recommendations)
    optimized_df.to_csv('optimized_model_recommendations.csv', index=False, encoding='utf-8-sig')
    
    # 保存性能对比
    comparison_data = []
    for metric, chinese in zip(metrics_names, metrics_chinese):
        comparison_data.append({
            '指标': chinese,
            '原始模型': original_metrics[metric],
            '优化模型': optimized_metrics[metric],
            '提升幅度(%)': improvements[metric]
        })
    
    comparison_df = pd.DataFrame(comparison_data)
    comparison_df.to_csv('model_performance_comparison.csv', index=False, encoding='utf-8-sig')
    
    # 生成可视化对比
    create_comparison_visualization(original_metrics, optimized_metrics, 
                                  original_recommendations, optimized_recommendations)
    
    print("✅ 结果保存完成:")
    print("   - original_model_recommendations.csv (原始模型推荐)")
    print("   - optimized_model_recommendations.csv (优化模型推荐)")
    print("   - model_performance_comparison.csv (性能对比)")
    print("   - model_comparison_visualization.png (可视化对比)")
    
    # 总结
    print("\n" + "=" * 80)
    print("🎉 优化总结")
    print("=" * 80)
    
    avg_improvement = np.mean(list(improvements.values()))
    print(f"📈 平均性能提升: {avg_improvement:.2f}%")
    print(f"🏆 最大提升指标: {max(improvements, key=improvements.get)} (+{max(improvements.values()):.2f}%)")
    print(f"📊 F1分数提升: {improvements['f1']:.2f}%")
    print(f"🎯 AUC值提升: {improvements['auc']:.2f}%")
    
    print("\n🔧 主要优化技术:")
    print("   ✓ 增加模型深度和宽度 (8层, 256维, 16头)")
    print("   ✓ 多尺度池化 (平均池化 + 最大池化 + 注意力池化)")
    print("   ✓ Focal Loss处理类别不平衡")
    print("   ✓ K折交叉验证提高泛化能力")
    print("   ✓ 加权采样平衡训练数据")
    print("   ✓ 余弦退火学习率调度")
    print("   ✓ 梯度裁剪防止梯度爆炸")
    print("   ✓ Pre-norm Transformer架构")
    
    return original_metrics, optimized_metrics, original_recommendations, optimized_recommendations

def create_comparison_visualization(original_metrics, optimized_metrics, 
                                  original_recs, optimized_recs):
    """创建对比可视化"""
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('深度学习股票分析系统 - 模型优化对比', fontsize=16, fontweight='bold')
    
    # 1. 性能指标对比
    ax1 = axes[0, 0]
    metrics = ['accuracy', 'precision', 'recall', 'f1', 'auc']
    metrics_chinese = ['准确率', '精确率', '召回率', 'F1分数', 'AUC']
    
    original_vals = [original_metrics[m] for m in metrics]
    optimized_vals = [optimized_metrics[m] for m in metrics]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, original_vals, width, label='原始模型', color='#FF6B6B', alpha=0.8)
    bars2 = ax1.bar(x + width/2, optimized_vals, width, label='优化模型', color='#4ECDC4', alpha=0.8)
    
    ax1.set_xlabel('性能指标')
    ax1.set_ylabel('分数')
    ax1.set_title('模型性能对比')
    ax1.set_xticks(x)
    ax1.set_xticklabels(metrics_chinese, rotation=45)
    ax1.legend()
    ax1.grid(axis='y', alpha=0.3)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)
    
    # 2. 提升幅度
    ax2 = axes[0, 1]
    improvements = [((optimized_vals[i] - original_vals[i]) / original_vals[i]) * 100 
                   for i in range(len(metrics))]
    
    bars = ax2.bar(metrics_chinese, improvements, color='#45B7D1', alpha=0.8)
    ax2.set_xlabel('性能指标')
    ax2.set_ylabel('提升幅度 (%)')
    ax2.set_title('性能提升幅度')
    ax2.grid(axis='y', alpha=0.3)
    
    # 添加数值标签
    for bar, imp in zip(bars, improvements):
        ax2.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 1,
                f'{imp:.1f}%', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
    
    # 3. 原始模型推荐概率
    ax3 = axes[1, 0]
    if original_recs:
        names = [rec['stock_name'][:4] for rec in original_recs[:8]]
        probs = [rec['probability_percent'] for rec in original_recs[:8]]
        
        bars = ax3.barh(range(len(names)), probs, color='#FF6B6B', alpha=0.8)
        ax3.set_yticks(range(len(names)))
        ax3.set_yticklabels(names)
        ax3.set_xlabel('翻倍概率 (%)')
        ax3.set_title('原始模型 - 前8只推荐股票')
        
        for i, (bar, prob) in enumerate(zip(bars, probs)):
            ax3.text(bar.get_width() + 0.5, bar.get_y() + bar.get_height()/2, 
                    f'{prob:.1f}%', va='center', fontsize=9)
    
    # 4. 优化模型推荐概率
    ax4 = axes[1, 1]
    if optimized_recs:
        names = [rec['stock_name'][:4] for rec in optimized_recs[:8]]
        probs = [rec['probability_percent'] for rec in optimized_recs[:8]]
        
        bars = ax4.barh(range(len(names)), probs, color='#4ECDC4', alpha=0.8)
        ax4.set_yticks(range(len(names)))
        ax4.set_yticklabels(names)
        ax4.set_xlabel('翻倍概率 (%)')
        ax4.set_title('优化模型 - 前8只推荐股票')
        
        for i, (bar, prob) in enumerate(zip(bars, probs)):
            ax4.text(bar.get_width() + 0.5, bar.get_y() + bar.get_height()/2, 
                    f'{prob:.1f}%', va='center', fontsize=9)
    
    plt.tight_layout()
    plt.savefig('model_comparison_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    compare_models()
