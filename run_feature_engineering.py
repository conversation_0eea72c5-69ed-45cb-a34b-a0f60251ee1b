"""
运行增强特征工程和相关性分析
"""

from enhanced_stock_analyzer import EnhancedStockAnalyzer
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

def main():
    print("=== 开始增强特征工程和相关性分析 ===")
    
    # 初始化分析器
    analyzer = EnhancedStockAnalyzer('缺口股票时序数据集_20250625_213940.csv')
    
    # 加载数据
    raw_data = analyzer.load_and_preprocess_data()
    
    # 读取高翻倍潜力股票列表
    high_potential_stocks = pd.read_csv('高翻倍潜力股票.csv')
    
    # 选择前50只高潜力股票进行详细分析（减少计算量）
    top_stocks = high_potential_stocks.head(50)['股票代码'].tolist()
    
    print(f"选择前50只高潜力股票进行特征工程分析...")
    
    # 处理股票数据并计算增强特征
    all_processed_data = []
    
    for i, stock_code in enumerate(tqdm(top_stocks, desc="处理股票数据")):
        try:
            stock_data = analyzer.process_stock_with_enhanced_features(stock_code)
            all_processed_data.append(stock_data)
        except Exception as e:
            print(f"处理股票 {stock_code} 时出错: {e}")
            continue
    
    # 合并所有处理后的数据
    if all_processed_data:
        processed_data = pd.concat(all_processed_data, ignore_index=True)
        print(f"成功处理 {len(all_processed_data)} 只股票的数据")
        
        # 保存处理后的数据
        processed_data.to_csv('增强特征数据.csv', index=False, encoding='utf-8-sig')
        
        # 计算特征相关性
        correlation_results = analyzer.calculate_feature_correlation_with_doubling(processed_data)
        
        # 保存相关性结果
        correlation_results.to_csv('特征相关性分析.csv', index=False, encoding='utf-8-sig')
        
        # 分析和可视化相关性结果
        analyze_correlation_results(correlation_results)
        
        # 生成特征重要性排序
        generate_feature_importance_ranking(correlation_results)
        
    else:
        print("没有成功处理任何股票数据")

def analyze_correlation_results(correlation_df):
    """分析相关性结果"""
    print("\n=== 特征相关性分析结果 ===")
    
    # 按目标变量分组分析
    for target in ['is_double_10w', 'is_double_20w', 'is_double_30w', 'is_double_50w']:
        target_corr = correlation_df[correlation_df['目标变量'] == target].copy()
        target_corr = target_corr.sort_values('绝对相关系数', ascending=False)
        
        print(f"\n{target} 相关性最高的前10个特征:")
        print(target_corr[['特征名称', '皮尔逊相关系数', '互信息得分']].head(10).to_string(index=False))
    
    # 创建相关性热力图
    create_correlation_heatmap(correlation_df)

def create_correlation_heatmap(correlation_df):
    """创建相关性热力图"""
    print("\n正在生成相关性热力图...")
    
    # 准备数据用于热力图
    pivot_data = correlation_df.pivot(index='特征名称', columns='目标变量', values='皮尔逊相关系数')
    
    # 创建图表
    plt.figure(figsize=(12, 20))
    
    # 绘制热力图
    sns.heatmap(pivot_data, 
                annot=True, 
                cmap='RdBu_r', 
                center=0,
                fmt='.3f',
                cbar_kws={'label': '皮尔逊相关系数'})
    
    plt.title('技术指标与翻倍概率相关性热力图', fontsize=16, fontweight='bold')
    plt.xlabel('翻倍周期', fontsize=12)
    plt.ylabel('技术指标', fontsize=12)
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    
    plt.tight_layout()
    plt.savefig('特征相关性热力图.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_feature_importance_ranking(correlation_df):
    """生成特征重要性排序"""
    print("\n=== 生成特征重要性排序 ===")
    
    # 计算综合重要性得分
    feature_importance = []
    
    for feature in correlation_df['特征名称'].unique():
        feature_data = correlation_df[correlation_df['特征名称'] == feature]
        
        # 计算平均绝对相关系数
        avg_abs_corr = feature_data['绝对相关系数'].mean()
        
        # 计算平均互信息得分
        avg_mi_score = feature_data['互信息得分'].mean()
        
        # 计算最大绝对相关系数
        max_abs_corr = feature_data['绝对相关系数'].max()
        
        # 综合得分（可以调整权重）
        composite_score = 0.4 * avg_abs_corr + 0.3 * avg_mi_score + 0.3 * max_abs_corr
        
        feature_importance.append({
            '特征名称': feature,
            '平均绝对相关系数': avg_abs_corr,
            '平均互信息得分': avg_mi_score,
            '最大绝对相关系数': max_abs_corr,
            '综合重要性得分': composite_score
        })
    
    # 转换为DataFrame并排序
    importance_df = pd.DataFrame(feature_importance)
    importance_df = importance_df.sort_values('综合重要性得分', ascending=False)
    
    print("\n特征重要性排序 (前20名):")
    print(importance_df.head(20).to_string(index=False))
    
    # 保存特征重要性排序
    importance_df.to_csv('特征重要性排序.csv', index=False, encoding='utf-8-sig')
    
    # 可视化特征重要性
    plt.figure(figsize=(12, 8))
    
    top_features = importance_df.head(15)
    plt.barh(range(len(top_features)), top_features['综合重要性得分'], alpha=0.7)
    plt.yticks(range(len(top_features)), top_features['特征名称'])
    plt.xlabel('综合重要性得分')
    plt.title('技术指标重要性排序 (前15名)', fontsize=14, fontweight='bold')
    plt.gca().invert_yaxis()
    
    # 添加数值标签
    for i, v in enumerate(top_features['综合重要性得分']):
        plt.text(v + 0.001, i, f'{v:.3f}', va='center')
    
    plt.tight_layout()
    plt.savefig('特征重要性排序.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return importance_df

if __name__ == "__main__":
    main()
