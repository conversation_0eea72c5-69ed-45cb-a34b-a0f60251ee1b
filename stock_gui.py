"""
股票分析GUI应用程序
基于PyQt5的深度学习股票预测系统
"""

import sys
import os
import pandas as pd
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QGridLayout, QLabel, QPushButton, 
                             QFileDialog, QTextEdit, QTableWidget, QTableWidgetItem,
                             QSlider, QSpinBox, QProgressBar, QTabWidget,
                             QGroupBox, QSplitter, QMessageBox, QComboBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib
matplotlib.use('Qt5Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['mathtext.fontset'] = 'stix'

from data_analyzer import StockDataAnalyzer
from stock_transformer import StockPredictor
from optimized_transformer import OptimizedStockPredictor

class AnalysisThread(QThread):
    """分析线程"""
    progress = pyqtSignal(int)
    status = pyqtSignal(str)
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)

    def __init__(self, file_path, sequence_length, prediction_weeks, epochs, use_optimized=True):
        super().__init__()
        self.file_path = file_path
        self.sequence_length = sequence_length
        self.prediction_weeks = prediction_weeks
        self.epochs = epochs
        self.use_optimized = use_optimized
    
    def run(self):
        try:
            # 数据加载
            self.status.emit("正在加载数据...")
            self.progress.emit(10)
            
            analyzer = StockDataAnalyzer(self.file_path)
            data = analyzer.process_all_stocks()
            
            self.status.emit("数据处理完成，开始训练模型...")
            self.progress.emit(30)
            
            # 模型训练
            if self.use_optimized:
                predictor = OptimizedStockPredictor(
                    sequence_length=self.sequence_length,
                    prediction_weeks=self.prediction_weeks
                )

                self.status.emit("正在训练优化版深度学习模型...")
                self.progress.emit(50)

                metrics = predictor.train_with_kfold(
                    data,
                    n_splits=3,
                    batch_size=32,
                    epochs=self.epochs,
                    learning_rate=0.0003
                )
            else:
                predictor = StockPredictor(
                    sequence_length=self.sequence_length,
                    prediction_weeks=self.prediction_weeks
                )

                self.status.emit("正在训练标准深度学习模型...")
                self.progress.emit(50)

                metrics = predictor.train(
                    data,
                    test_size=0.2,
                    batch_size=64,
                    epochs=self.epochs,
                    learning_rate=0.0005
                )
            
            self.status.emit("生成预测结果...")
            self.progress.emit(80)
            
            # 生成预测
            predictions = predictor.predict(data)
            recommendations = predictor.get_top_recommendations(predictions, top_n=20)
            
            self.progress.emit(100)
            self.status.emit("分析完成！")
            
            # 返回结果
            result = {
                'data': data,
                'metrics': metrics,
                'predictions': predictions,
                'recommendations': recommendations,
                'predictor': predictor
            }
            
            self.finished.emit(result)
            
        except Exception as e:
            self.error.emit(str(e))

class PlotCanvas(FigureCanvas):
    """绘图画布"""
    
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        super().__init__(self.fig)
        self.setParent(parent)
        
    def plot_overview(self, data):
        """绘制数据概览"""
        self.fig.clear()
        
        # 创建子图
        gs = self.fig.add_gridspec(2, 2, hspace=0.3, wspace=0.3)
        
        # 翻倍概率分布
        ax1 = self.fig.add_subplot(gs[0, 0])
        weeks = [10, 20, 50]
        probs = []
        for w in weeks:
            col = f'double_{w}w'
            if col in data.columns:
                probs.append(data[col].mean() * 100)
            else:
                probs.append(0)
        
        ax1.bar(weeks, probs, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
        ax1.set_title('不同周期翻倍概率', fontsize=12)
        ax1.set_xlabel('周数')
        ax1.set_ylabel('概率 (%)')
        
        # 价格变化率分布
        ax2 = self.fig.add_subplot(gs[0, 1])
        if '价格变化率' in data.columns:
            ax2.hist(data['价格变化率'].dropna(), bins=50, alpha=0.7, color='#96CEB4')
            ax2.set_title('价格变化率分布', fontsize=12)
            ax2.set_xlabel('变化率')
            ax2.set_ylabel('频次')
        
        # RSI分布
        ax3 = self.fig.add_subplot(gs[1, 0])
        if 'RSI' in data.columns:
            ax3.hist(data['RSI'].dropna(), bins=30, alpha=0.7, color='#FFEAA7')
            ax3.axvline(30, color='red', linestyle='--', alpha=0.7, label='超卖线')
            ax3.axvline(70, color='red', linestyle='--', alpha=0.7, label='超买线')
            ax3.set_title('RSI指标分布', fontsize=12)
            ax3.set_xlabel('RSI值')
            ax3.set_ylabel('频次')
            ax3.legend()
        
        # 成交量比率分布
        ax4 = self.fig.add_subplot(gs[1, 1])
        if '成交量比率' in data.columns:
            ax4.hist(data['成交量比率'].dropna(), bins=30, alpha=0.7, color='#DDA0DD')
            ax4.set_title('成交量比率分布', fontsize=12)
            ax4.set_xlabel('成交量比率')
            ax4.set_ylabel('频次')
        
        self.draw()
    
    def plot_recommendations(self, recommendations):
        """绘制推荐股票"""
        self.fig.clear()
        
        if not recommendations:
            return
        
        # 提取数据
        names = [rec['stock_name'][:4] for rec in recommendations[:10]]
        probs = [rec['probability_percent'] for rec in recommendations[:10]]
        
        # 创建水平条形图
        ax = self.fig.add_subplot(111)
        bars = ax.barh(range(len(names)), probs, color='#45B7D1')
        
        # 设置标签
        ax.set_yticks(range(len(names)))
        ax.set_yticklabels(names)
        ax.set_xlabel('翻倍概率 (%)')
        ax.set_title('前10只推荐股票', fontsize=14, fontweight='bold')
        
        # 添加数值标签
        for i, (bar, prob) in enumerate(zip(bars, probs)):
            ax.text(bar.get_width() + 0.5, bar.get_y() + bar.get_height()/2, 
                   f'{prob:.1f}%', va='center', fontsize=10)
        
        # 美化图表
        ax.grid(axis='x', alpha=0.3)
        ax.set_xlim(0, max(probs) * 1.1)
        
        self.draw()

class StockAnalysisGUI(QMainWindow):
    """股票分析主窗口"""
    
    def __init__(self):
        super().__init__()
        self.data = None
        self.predictions = None
        self.recommendations = None
        self.predictor = None
        
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('深度学习股票分析系统 v1.0')
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                font-size: 14px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        self.create_control_panel(splitter)
        
        # 右侧结果面板
        self.create_result_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([400, 1000])
        
        # 状态栏
        self.statusBar().showMessage('准备就绪')
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        
        # 数据导入组
        data_group = QGroupBox("数据导入")
        data_layout = QVBoxLayout(data_group)
        
        self.file_label = QLabel("未选择文件")
        self.file_label.setStyleSheet("color: #666; padding: 5px;")
        data_layout.addWidget(self.file_label)
        
        self.select_file_btn = QPushButton("选择CSV文件")
        self.select_file_btn.clicked.connect(self.select_file)
        data_layout.addWidget(self.select_file_btn)
        
        control_layout.addWidget(data_group)
        
        # 参数设置组
        param_group = QGroupBox("模型参数")
        param_layout = QGridLayout(param_group)
        
        # 序列长度
        param_layout.addWidget(QLabel("序列长度:"), 0, 0)
        self.sequence_slider = QSlider(Qt.Horizontal)
        self.sequence_slider.setRange(20, 100)
        self.sequence_slider.setValue(30)
        self.sequence_slider.valueChanged.connect(self.update_sequence_label)
        param_layout.addWidget(self.sequence_slider, 0, 1)
        
        self.sequence_label = QLabel("30")
        param_layout.addWidget(self.sequence_label, 0, 2)
        
        # 预测周期
        param_layout.addWidget(QLabel("预测周期:"), 1, 0)
        self.weeks_combo = QComboBox()
        self.weeks_combo.addItems(["10周", "20周", "50周"])
        self.weeks_combo.setCurrentText("50周")
        param_layout.addWidget(self.weeks_combo, 1, 1, 1, 2)
        
        # 训练轮次
        param_layout.addWidget(QLabel("训练轮次:"), 2, 0)
        self.epochs_spin = QSpinBox()
        self.epochs_spin.setRange(10, 200)
        self.epochs_spin.setValue(50)
        param_layout.addWidget(self.epochs_spin, 2, 1, 1, 2)

        # 模型类型
        param_layout.addWidget(QLabel("模型类型:"), 3, 0)
        self.model_combo = QComboBox()
        self.model_combo.addItems(["优化模型 (推荐)", "标准模型"])
        self.model_combo.setCurrentText("优化模型 (推荐)")
        param_layout.addWidget(self.model_combo, 3, 1, 1, 2)

        control_layout.addWidget(param_group)
        
        # 分析按钮
        self.analyze_btn = QPushButton("开始分析")
        self.analyze_btn.setEnabled(False)
        self.analyze_btn.clicked.connect(self.start_analysis)
        self.analyze_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                font-size: 16px;
                font-weight: bold;
                padding: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        control_layout.addWidget(self.analyze_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        control_layout.addWidget(self.progress_bar)
        
        # 状态文本
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        control_layout.addWidget(self.status_text)
        
        # 添加弹性空间
        control_layout.addStretch()
        
        parent.addWidget(control_widget)
        
    def create_result_panel(self, parent):
        """创建结果面板"""
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 数据概览标签页
        self.overview_tab = QWidget()
        overview_layout = QVBoxLayout(self.overview_tab)
        
        self.overview_canvas = PlotCanvas(self.overview_tab, width=8, height=6)
        overview_layout.addWidget(self.overview_canvas)
        
        self.tab_widget.addTab(self.overview_tab, "数据概览")
        
        # 推荐股票标签页
        self.recommendations_tab = QWidget()
        rec_layout = QVBoxLayout(self.recommendations_tab)
        
        # 推荐图表
        self.rec_canvas = PlotCanvas(self.recommendations_tab, width=8, height=4)
        rec_layout.addWidget(self.rec_canvas)
        
        # 推荐表格
        self.rec_table = QTableWidget()
        self.rec_table.setMaximumHeight(300)
        rec_layout.addWidget(self.rec_table)
        
        self.tab_widget.addTab(self.recommendations_tab, "推荐股票")
        
        # 模型性能标签页
        self.performance_tab = QWidget()
        perf_layout = QVBoxLayout(self.performance_tab)
        
        self.performance_text = QTextEdit()
        self.performance_text.setReadOnly(True)
        perf_layout.addWidget(self.performance_text)
        
        self.tab_widget.addTab(self.performance_tab, "模型性能")
        
        parent.addWidget(self.tab_widget)
    
    def update_sequence_label(self, value):
        """更新序列长度标签"""
        self.sequence_label.setText(str(value))
    
    def select_file(self):
        """选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择股票数据文件", "", "CSV文件 (*.csv)"
        )
        
        if file_path:
            self.file_path = file_path
            self.file_label.setText(f"已选择: {os.path.basename(file_path)}")
            self.analyze_btn.setEnabled(True)
            self.log_message(f"已选择文件: {file_path}")
    
    def start_analysis(self):
        """开始分析"""
        if not hasattr(self, 'file_path'):
            QMessageBox.warning(self, "警告", "请先选择数据文件！")
            return
        
        # 获取参数
        sequence_length = self.sequence_slider.value()
        prediction_weeks = int(self.weeks_combo.currentText().replace("周", ""))
        epochs = self.epochs_spin.value()
        
        # 禁用按钮，显示进度条
        self.analyze_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 创建分析线程
        use_optimized = self.model_combo.currentText().startswith("优化模型")
        self.analysis_thread = AnalysisThread(
            self.file_path, sequence_length, prediction_weeks, epochs, use_optimized
        )
        
        # 连接信号
        self.analysis_thread.progress.connect(self.progress_bar.setValue)
        self.analysis_thread.status.connect(self.log_message)
        self.analysis_thread.finished.connect(self.analysis_finished)
        self.analysis_thread.error.connect(self.analysis_error)
        
        # 启动线程
        self.analysis_thread.start()
        
        self.log_message("开始分析...")
    
    def analysis_finished(self, result):
        """分析完成"""
        self.data = result['data']
        self.predictions = result['predictions']
        self.recommendations = result['recommendations']
        self.predictor = result['predictor']
        metrics = result['metrics']
        
        # 更新界面
        self.update_overview()
        self.update_recommendations()
        self.update_performance(metrics)
        
        # 恢复按钮状态
        self.analyze_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        self.log_message("分析完成！")
        self.statusBar().showMessage('分析完成')
        
        QMessageBox.information(self, "完成", "股票分析已完成！")
    
    def analysis_error(self, error_msg):
        """分析错误"""
        self.analyze_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        self.log_message(f"错误: {error_msg}")
        QMessageBox.critical(self, "错误", f"分析过程中发生错误:\n{error_msg}")
    
    def update_overview(self):
        """更新数据概览"""
        if self.data is not None:
            self.overview_canvas.plot_overview(self.data)
    
    def update_recommendations(self):
        """更新推荐股票"""
        if self.recommendations:
            # 更新图表
            self.rec_canvas.plot_recommendations(self.recommendations)
            
            # 更新表格
            self.rec_table.setRowCount(len(self.recommendations))
            self.rec_table.setColumnCount(6)
            self.rec_table.setHorizontalHeaderLabels([
                "排名", "股票代码", "股票名称", "当前价格", "翻倍概率", "最新日期"
            ])
            
            for i, rec in enumerate(self.recommendations):
                self.rec_table.setItem(i, 0, QTableWidgetItem(str(rec['rank'])))
                self.rec_table.setItem(i, 1, QTableWidgetItem(rec['stock_code']))
                self.rec_table.setItem(i, 2, QTableWidgetItem(rec['stock_name']))
                self.rec_table.setItem(i, 3, QTableWidgetItem(f"{rec['current_price']:.2f}"))
                self.rec_table.setItem(i, 4, QTableWidgetItem(f"{rec['probability_percent']:.2f}%"))
                self.rec_table.setItem(i, 5, QTableWidgetItem(str(rec['latest_date'])[:10]))
            
            self.rec_table.resizeColumnsToContents()
    
    def update_performance(self, metrics):
        """更新模型性能"""
        performance_text = f"""
模型性能评估结果:

准确率 (Accuracy): {metrics['accuracy']:.4f}
精确率 (Precision): {metrics['precision']:.4f}
召回率 (Recall): {metrics['recall']:.4f}
F1分数 (F1-Score): {metrics['f1']:.4f}
AUC值: {metrics['auc']:.4f}

性能解释:
- 准确率: 模型预测正确的比例
- 精确率: 预测为正例中实际为正例的比例
- 召回率: 实际正例中被正确预测的比例
- F1分数: 精确率和召回率的调和平均数
- AUC值: 受试者工作特征曲线下面积，越接近1越好

风险提示:
1. 本预测结果仅供参考，不构成投资建议
2. 股票投资存在风险，请谨慎决策
3. 模型基于历史数据训练，未来表现可能不同
4. 建议结合其他分析方法综合判断
        """
        
        self.performance_text.setText(performance_text)
    
    def log_message(self, message):
        """记录日志消息"""
        self.status_text.append(f"[{pd.Timestamp.now().strftime('%H:%M:%S')}] {message}")
        self.status_text.verticalScrollBar().setValue(
            self.status_text.verticalScrollBar().maximum()
        )

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("深度学习股票分析系统")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = StockAnalysisGUI()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
