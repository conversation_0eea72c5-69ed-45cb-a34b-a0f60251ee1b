"""
基于Transformer的股票预测模型
使用时序数据预测股票翻倍概率
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.utils.class_weight import compute_class_weight
import math
import warnings
warnings.filterwarnings('ignore')

class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :]

class StockTransformer(nn.Module):
    """股票预测Transformer模型"""
    
    def __init__(self, input_dim, d_model=128, nhead=8, num_layers=6, 
                 sequence_length=50, dropout=0.1, num_classes=1):
        super(StockTransformer, self).__init__()
        
        self.input_dim = input_dim
        self.d_model = d_model
        self.sequence_length = sequence_length
        
        # 输入投影层
        self.input_projection = nn.Linear(input_dim, d_model)
        
        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, d_model // 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, num_classes)
        )
        
        # 全局池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
    def forward(self, x):
        # x shape: (batch_size, sequence_length, input_dim)
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x = self.input_projection(x)  # (batch_size, seq_len, d_model)
        
        # 位置编码
        x = x.transpose(0, 1)  # (seq_len, batch_size, d_model)
        x = self.pos_encoder(x)
        x = x.transpose(0, 1)  # (batch_size, seq_len, d_model)
        
        # Transformer编码
        x = self.transformer_encoder(x)  # (batch_size, seq_len, d_model)
        
        # 全局平均池化
        x = x.transpose(1, 2)  # (batch_size, d_model, seq_len)
        x = self.global_pool(x)  # (batch_size, d_model, 1)
        x = x.squeeze(-1)  # (batch_size, d_model)
        
        # 分类
        output = self.classifier(x)  # (batch_size, num_classes)
        
        return output  # 不使用sigmoid，因为BCEWithLogitsLoss内部会处理

class StockDataset(Dataset):
    """股票数据集"""
    
    def __init__(self, sequences, targets):
        self.sequences = torch.FloatTensor(sequences)
        self.targets = torch.FloatTensor(targets)
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return self.sequences[idx], self.targets[idx]

class StockPredictor:
    """股票预测器"""
    
    def __init__(self, sequence_length=50, prediction_weeks=50):
        self.sequence_length = sequence_length
        self.prediction_weeks = prediction_weeks
        self.model = None
        self.scaler = RobustScaler()
        self.feature_columns = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
    
    def prepare_sequences(self, data):
        """准备时序数据"""
        sequences = []
        targets = []
        
        # 按股票分组
        for stock_code in data['股票代码'].unique():
            stock_data = data[data['股票代码'] == stock_code].sort_values('交易日期')
            
            if len(stock_data) < self.sequence_length + 1:
                continue
            
            # 获取特征和目标
            features = stock_data[self.feature_columns].values
            target_col = f'double_{self.prediction_weeks}w'
            
            if target_col not in stock_data.columns:
                continue
            
            targets_stock = stock_data[target_col].values
            
            # 创建序列
            for i in range(len(stock_data) - self.sequence_length):
                if i + self.sequence_length < len(targets_stock):
                    seq = features[i:i + self.sequence_length]
                    target = targets_stock[i + self.sequence_length]
                    
                    if not np.isnan(target):
                        sequences.append(seq)
                        targets.append(target)
        
        return np.array(sequences), np.array(targets)
    
    def train(self, data, test_size=0.2, batch_size=32, epochs=100, learning_rate=0.001):
        """训练模型"""
        print("准备训练数据...")
        
        # 获取特征列
        self.feature_columns = [
            '价格变化率', '振幅', '上影线比例', '下影线比例',
            'MA5_ratio', 'MA10_ratio', 'MA20_ratio', 'MA50_ratio',
            '成交量比率', 'RSI', 'MACD', 'MACD_signal', 'MACD_histogram',
            'BB_position', '价格位置_52周'
        ]
        
        # 准备序列数据
        sequences, targets = self.prepare_sequences(data)
        print(f"生成序列数据: {len(sequences)}个样本")
        print(f"正样本比例: {targets.mean():.3f}")
        
        if len(sequences) == 0:
            raise ValueError("没有足够的数据生成序列")
        
        # 标准化特征
        sequences_reshaped = sequences.reshape(-1, sequences.shape[-1])
        sequences_scaled = self.scaler.fit_transform(sequences_reshaped)
        sequences_scaled = sequences_scaled.reshape(sequences.shape)
        
        # 划分训练测试集
        X_train, X_test, y_train, y_test = train_test_split(
            sequences_scaled, targets, test_size=test_size, random_state=42, stratify=targets
        )
        
        print(f"训练集: {len(X_train)}个样本")
        print(f"测试集: {len(X_test)}个样本")
        
        # 创建数据加载器
        train_dataset = StockDataset(X_train, y_train)
        test_dataset = StockDataset(X_test, y_test)
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
        
        # 创建模型
        input_dim = len(self.feature_columns)
        self.model = StockTransformer(
            input_dim=input_dim,
            d_model=128,
            nhead=8,
            num_layers=6,
            sequence_length=self.sequence_length,
            dropout=0.1
        ).to(self.device)
        
        # 计算类别权重以处理不平衡数据
        class_weights = compute_class_weight('balanced', classes=np.unique(y_train), y=y_train)
        pos_weight = torch.FloatTensor([class_weights[1] / class_weights[0]]).to(self.device)

        # 损失函数和优化器
        criterion = nn.BCEWithLogitsLoss(pos_weight=pos_weight)
        optimizer = optim.AdamW(self.model.parameters(), lr=learning_rate, weight_decay=0.01)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=10, factor=0.5)
        
        # 训练循环
        best_val_loss = float('inf')
        patience_counter = 0
        
        print("开始训练...")
        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0.0
            train_preds = []
            train_targets = []
            
            for batch_x, batch_y in train_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_x).squeeze()
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()

                train_loss += loss.item()
                # 对于BCEWithLogitsLoss，需要应用sigmoid来获取概率
                probs = torch.sigmoid(outputs).detach().cpu().numpy()
                train_preds.extend(probs)
                train_targets.extend(batch_y.detach().cpu().numpy())
            
            # 验证阶段
            self.model.eval()
            val_loss = 0.0
            val_preds = []
            val_targets = []
            
            with torch.no_grad():
                for batch_x, batch_y in test_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                    outputs = self.model(batch_x).squeeze()
                    loss = criterion(outputs, batch_y)

                    val_loss += loss.item()
                    # 对于BCEWithLogitsLoss，需要应用sigmoid来获取概率
                    probs = torch.sigmoid(outputs).cpu().numpy()
                    val_preds.extend(probs)
                    val_targets.extend(batch_y.cpu().numpy())
            
            # 计算指标
            train_loss /= len(train_loader)
            val_loss /= len(test_loader)
            
            train_auc = roc_auc_score(train_targets, train_preds)
            val_auc = roc_auc_score(val_targets, val_preds)
            
            scheduler.step(val_loss)
            
            if (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch+1}/{epochs}")
                print(f"  训练损失: {train_loss:.4f}, 训练AUC: {train_auc:.4f}")
                print(f"  验证损失: {val_loss:.4f}, 验证AUC: {val_auc:.4f}")
                print(f"  学习率: {optimizer.param_groups[0]['lr']:.6f}")
            
            # 早停
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'best_stock_model.pth')
            else:
                patience_counter += 1
                if patience_counter >= 20:
                    print("早停触发")
                    break
        
        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_stock_model.pth'))
        
        # 最终评估
        self.model.eval()
        final_preds = []
        final_targets = []
        
        with torch.no_grad():
            for batch_x, batch_y in test_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                outputs = self.model(batch_x).squeeze()
                # 对于BCEWithLogitsLoss，需要应用sigmoid来获取概率
                probs = torch.sigmoid(outputs).cpu().numpy()
                final_preds.extend(probs)
                final_targets.extend(batch_y.cpu().numpy())
        
        # 计算最终指标
        final_preds_binary = (np.array(final_preds) > 0.5).astype(int)
        final_targets = np.array(final_targets)
        
        accuracy = accuracy_score(final_targets, final_preds_binary)
        precision = precision_score(final_targets, final_preds_binary)
        recall = recall_score(final_targets, final_preds_binary)
        f1 = f1_score(final_targets, final_preds_binary)
        auc = roc_auc_score(final_targets, final_preds)
        
        print("\n最终测试结果:")
        print(f"准确率: {accuracy:.4f}")
        print(f"精确率: {precision:.4f}")
        print(f"召回率: {recall:.4f}")
        print(f"F1分数: {f1:.4f}")
        print(f"AUC: {auc:.4f}")
        
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'auc': auc
        }
    
    def predict(self, data):
        """预测股票翻倍概率"""
        if self.model is None:
            raise ValueError("模型尚未训练")

        self.model.eval()
        predictions = {}

        with torch.no_grad():
            for stock_code in data['股票代码'].unique():
                stock_data = data[data['股票代码'] == stock_code].sort_values('交易日期')

                if len(stock_data) < self.sequence_length:
                    continue

                # 获取最新序列
                features = stock_data[self.feature_columns].values[-self.sequence_length:]

                # 正确的标准化方法
                features_reshaped = features.reshape(-1, features.shape[-1])
                features_scaled = self.scaler.transform(features_reshaped)
                features_scaled = features_scaled.reshape(features.shape)

                # 预测
                sequence = torch.FloatTensor(features_scaled).unsqueeze(0).to(self.device)
                logits = self.model(sequence)
                prob = torch.sigmoid(logits).cpu().numpy()[0, 0]

                predictions[stock_code] = {
                    'stock_name': stock_data['股票名称'].iloc[-1],
                    'current_price': stock_data['收盘价'].iloc[-1],
                    'double_probability': prob,
                    'latest_date': stock_data['交易日期'].iloc[-1]
                }

        return predictions
    
    def get_top_recommendations(self, predictions, top_n=10):
        """获取推荐股票"""
        # 按概率排序
        sorted_stocks = sorted(predictions.items(), key=lambda x: x[1]['double_probability'], reverse=True)
        
        recommendations = []
        for i, (stock_code, info) in enumerate(sorted_stocks[:top_n]):
            recommendations.append({
                'rank': i + 1,
                'stock_code': stock_code,
                'stock_name': info['stock_name'],
                'current_price': info['current_price'],
                'double_probability': info['double_probability'],
                'probability_percent': info['double_probability'] * 100,
                'latest_date': info['latest_date']
            })
        
        return recommendations

if __name__ == "__main__":
    # 测试模型
    from data_analyzer import StockDataAnalyzer
    
    print("加载数据...")
    analyzer = StockDataAnalyzer('缺口股票时序数据集_20250625_213940.csv')
    data = analyzer.process_all_stocks()
    
    print("训练模型...")
    predictor = StockPredictor(sequence_length=30, prediction_weeks=50)
    metrics = predictor.train(data, epochs=50)
    
    print("生成预测...")
    predictions = predictor.predict(data)
    recommendations = predictor.get_top_recommendations(predictions, top_n=10)
    
    print("\n=== 推荐股票 ===")
    for rec in recommendations:
        print(f"{rec['rank']}. {rec['stock_code']} {rec['stock_name']}")
        print(f"   当前价格: {rec['current_price']:.2f}")
        print(f"   翻倍概率: {rec['probability_percent']:.2f}%")
        print(f"   最新日期: {rec['latest_date']}")
        print()
