"""
优化版Transformer股票预测模型
包含多项性能优化技术
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.utils.class_weight import compute_class_weight
import math
import warnings
warnings.filterwarnings('ignore')

class ImprovedPositionalEncoding(nn.Module):
    """改进的位置编码"""
    
    def __init__(self, d_model, max_len=5000, dropout=0.1):
        super(ImprovedPositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)

class OptimizedStockTransformer(nn.Module):
    """优化版股票预测Transformer模型"""
    
    def __init__(self, input_dim, d_model=256, nhead=16, num_layers=8, 
                 sequence_length=50, dropout=0.2, num_classes=1):
        super(OptimizedStockTransformer, self).__init__()
        
        self.input_dim = input_dim
        self.d_model = d_model
        self.sequence_length = sequence_length
        
        # 输入投影层 - 增加层数
        self.input_projection = nn.Sequential(
            nn.Linear(input_dim, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, d_model),
            nn.LayerNorm(d_model)
        )
        
        # 改进的位置编码
        self.pos_encoder = ImprovedPositionalEncoding(d_model, dropout=dropout)
        
        # Transformer编码器 - 增加层数和头数
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True,
            norm_first=True  # Pre-norm架构
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 多尺度池化
        self.global_avg_pool = nn.AdaptiveAvgPool1d(1)
        self.global_max_pool = nn.AdaptiveMaxPool1d(1)
        
        # 注意力池化
        self.attention_pool = nn.Sequential(
            nn.Linear(d_model, d_model // 4),
            nn.Tanh(),
            nn.Linear(d_model // 4, 1),
            nn.Softmax(dim=1)
        )
        
        # 改进的分类头
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model * 3),  # 3种池化方式拼接
            nn.Dropout(dropout),
            nn.Linear(d_model * 3, d_model),
            nn.LayerNorm(d_model),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, d_model // 4),
            nn.LayerNorm(d_model // 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, num_classes)
        )
        
        # 权重初始化
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """权重初始化"""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
        
    def forward(self, x):
        # x shape: (batch_size, sequence_length, input_dim)
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x = self.input_projection(x)  # (batch_size, seq_len, d_model)
        
        # 位置编码
        x = x.transpose(0, 1)  # (seq_len, batch_size, d_model)
        x = self.pos_encoder(x)
        x = x.transpose(0, 1)  # (batch_size, seq_len, d_model)
        
        # Transformer编码
        x = self.transformer_encoder(x)  # (batch_size, seq_len, d_model)
        
        # 多尺度池化
        x_t = x.transpose(1, 2)  # (batch_size, d_model, seq_len)
        
        # 全局平均池化
        avg_pool = self.global_avg_pool(x_t).squeeze(-1)  # (batch_size, d_model)
        
        # 全局最大池化
        max_pool = self.global_max_pool(x_t).squeeze(-1)  # (batch_size, d_model)
        
        # 注意力池化
        attn_weights = self.attention_pool(x)  # (batch_size, seq_len, 1)
        attn_pool = torch.sum(x * attn_weights, dim=1)  # (batch_size, d_model)
        
        # 拼接所有池化结果
        pooled = torch.cat([avg_pool, max_pool, attn_pool], dim=1)  # (batch_size, d_model * 3)
        
        # 分类
        output = self.classifier(pooled)  # (batch_size, num_classes)
        
        return output

class FocalLoss(nn.Module):
    """Focal Loss用于处理类别不平衡"""
    
    def __init__(self, alpha=1, gamma=2, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs, targets):
        bce_loss = nn.functional.binary_cross_entropy_with_logits(inputs, targets, reduction='none')
        pt = torch.exp(-bce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * bce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class OptimizedStockPredictor:
    """优化版股票预测器"""
    
    def __init__(self, sequence_length=50, prediction_weeks=50):
        self.sequence_length = sequence_length
        self.prediction_weeks = prediction_weeks
        self.model = None
        self.scaler = RobustScaler()
        self.feature_columns = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
    
    def prepare_sequences(self, data):
        """准备时序数据"""
        sequences = []
        targets = []
        
        # 按股票分组
        for stock_code in data['股票代码'].unique():
            stock_data = data[data['股票代码'] == stock_code].sort_values('交易日期')
            
            if len(stock_data) < self.sequence_length + 1:
                continue
            
            # 获取特征和目标
            features = stock_data[self.feature_columns].values
            target_col = f'double_{self.prediction_weeks}w'
            
            if target_col not in stock_data.columns:
                continue
            
            targets_stock = stock_data[target_col].values
            
            # 创建序列 - 使用滑动窗口
            for i in range(len(stock_data) - self.sequence_length):
                if i + self.sequence_length < len(targets_stock):
                    seq = features[i:i + self.sequence_length]
                    target = targets_stock[i + self.sequence_length]
                    
                    if not np.isnan(target):
                        sequences.append(seq)
                        targets.append(target)
        
        return np.array(sequences), np.array(targets)
    
    def create_weighted_sampler(self, targets):
        """创建加权采样器"""
        class_counts = np.bincount(targets.astype(int))
        class_weights = 1.0 / class_counts
        sample_weights = class_weights[targets.astype(int)]
        
        return WeightedRandomSampler(
            weights=sample_weights,
            num_samples=len(sample_weights),
            replacement=True
        )
    
    def train_with_kfold(self, data, n_splits=5, batch_size=32, epochs=150, learning_rate=0.0003):
        """使用K折交叉验证训练"""
        print("准备训练数据...")
        
        # 获取特征列 - 增加更多特征
        self.feature_columns = [
            '价格变化率', '振幅', '上影线比例', '下影线比例',
            'MA5_ratio', 'MA10_ratio', 'MA20_ratio', 'MA50_ratio',
            '成交量比率', 'RSI', 'MACD', 'MACD_signal', 'MACD_histogram',
            'BB_position', '价格位置_52周'
        ]
        
        # 准备序列数据
        sequences, targets = self.prepare_sequences(data)
        print(f"生成序列数据: {len(sequences)}个样本")
        print(f"正样本比例: {targets.mean():.3f}")
        
        if len(sequences) == 0:
            raise ValueError("没有足够的数据生成序列")
        
        # 标准化特征
        sequences_reshaped = sequences.reshape(-1, sequences.shape[-1])
        sequences_scaled = self.scaler.fit_transform(sequences_reshaped)
        sequences_scaled = sequences_scaled.reshape(sequences.shape)
        
        # K折交叉验证
        kfold = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=42)
        fold_metrics = []
        
        for fold, (train_idx, val_idx) in enumerate(kfold.split(sequences_scaled, targets)):
            print(f"\n训练第 {fold + 1}/{n_splits} 折...")
            
            X_train, X_val = sequences_scaled[train_idx], sequences_scaled[val_idx]
            y_train, y_val = targets[train_idx], targets[val_idx]
            
            # 创建数据加载器
            train_dataset = StockDataset(X_train, y_train)
            val_dataset = StockDataset(X_val, y_val)
            
            # 使用加权采样
            train_sampler = self.create_weighted_sampler(y_train)
            train_loader = DataLoader(train_dataset, batch_size=batch_size, sampler=train_sampler)
            val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
            
            # 创建模型
            input_dim = len(self.feature_columns)
            model = OptimizedStockTransformer(
                input_dim=input_dim,
                d_model=256,
                nhead=16,
                num_layers=8,
                sequence_length=self.sequence_length,
                dropout=0.2
            ).to(self.device)
            
            # 损失函数和优化器
            criterion = FocalLoss(alpha=2, gamma=2)
            optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.01)
            scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=20, T_mult=2)
            
            # 训练循环
            best_val_f1 = 0
            patience_counter = 0
            
            for epoch in range(epochs):
                # 训练阶段
                model.train()
                train_loss = 0.0
                
                for batch_x, batch_y in train_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                    
                    optimizer.zero_grad()
                    outputs = model(batch_x).squeeze()
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    
                    # 梯度裁剪
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    
                    optimizer.step()
                    train_loss += loss.item()
                
                # 验证阶段
                model.eval()
                val_preds = []
                val_targets = []
                
                with torch.no_grad():
                    for batch_x, batch_y in val_loader:
                        batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                        outputs = model(batch_x).squeeze()
                        probs = torch.sigmoid(outputs).cpu().numpy()
                        val_preds.extend(probs)
                        val_targets.extend(batch_y.cpu().numpy())
                
                # 计算指标
                val_preds_binary = (np.array(val_preds) > 0.5).astype(int)
                val_targets = np.array(val_targets)
                
                val_f1 = f1_score(val_targets, val_preds_binary)
                
                scheduler.step()
                
                if val_f1 > best_val_f1:
                    best_val_f1 = val_f1
                    patience_counter = 0
                    if fold == 0:  # 保存第一折的最佳模型
                        self.model = model
                        torch.save(model.state_dict(), 'best_optimized_model.pth')
                else:
                    patience_counter += 1
                    if patience_counter >= 30:
                        print(f"  早停触发 (epoch {epoch + 1})")
                        break
                
                if (epoch + 1) % 20 == 0:
                    val_auc = roc_auc_score(val_targets, val_preds)
                    print(f"  Epoch {epoch+1}: F1={val_f1:.4f}, AUC={val_auc:.4f}")
            
            # 最终评估
            model.eval()
            final_preds = []
            final_targets = []
            
            with torch.no_grad():
                for batch_x, batch_y in val_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                    outputs = model(batch_x).squeeze()
                    probs = torch.sigmoid(outputs).cpu().numpy()
                    final_preds.extend(probs)
                    final_targets.extend(batch_y.cpu().numpy())
            
            # 计算折的指标
            final_preds_binary = (np.array(final_preds) > 0.5).astype(int)
            final_targets = np.array(final_targets)
            
            fold_metric = {
                'accuracy': accuracy_score(final_targets, final_preds_binary),
                'precision': precision_score(final_targets, final_preds_binary),
                'recall': recall_score(final_targets, final_preds_binary),
                'f1': f1_score(final_targets, final_preds_binary),
                'auc': roc_auc_score(final_targets, final_preds)
            }
            
            fold_metrics.append(fold_metric)
            print(f"第 {fold + 1} 折结果: F1={fold_metric['f1']:.4f}, AUC={fold_metric['auc']:.4f}")
        
        # 计算平均指标
        avg_metrics = {}
        for metric in ['accuracy', 'precision', 'recall', 'f1', 'auc']:
            avg_metrics[metric] = np.mean([fm[metric] for fm in fold_metrics])
            std = np.std([fm[metric] for fm in fold_metrics])
            print(f"{metric}: {avg_metrics[metric]:.4f} ± {std:.4f}")
        
        # 加载最佳模型
        if self.model is not None:
            self.model.load_state_dict(torch.load('best_optimized_model.pth'))
        
        return avg_metrics
    
    def predict(self, data):
        """预测股票翻倍概率"""
        if self.model is None:
            raise ValueError("模型尚未训练")
        
        self.model.eval()
        predictions = {}
        
        with torch.no_grad():
            for stock_code in data['股票代码'].unique():
                stock_data = data[data['股票代码'] == stock_code].sort_values('交易日期')
                
                if len(stock_data) < self.sequence_length:
                    continue
                
                # 获取最新序列
                features = stock_data[self.feature_columns].values[-self.sequence_length:]
                
                # 正确的标准化方法
                features_reshaped = features.reshape(-1, features.shape[-1])
                features_scaled = self.scaler.transform(features_reshaped)
                features_scaled = features_scaled.reshape(features.shape)
                
                # 预测
                sequence = torch.FloatTensor(features_scaled).unsqueeze(0).to(self.device)
                logits = self.model(sequence)
                prob = torch.sigmoid(logits).cpu().numpy()[0, 0]
                
                predictions[stock_code] = {
                    'stock_name': stock_data['股票名称'].iloc[-1],
                    'current_price': stock_data['收盘价'].iloc[-1],
                    'double_probability': prob,
                    'latest_date': stock_data['交易日期'].iloc[-1]
                }
        
        return predictions
    
    def get_top_recommendations(self, predictions, top_n=10):
        """获取推荐股票"""
        # 按概率排序
        sorted_stocks = sorted(predictions.items(), key=lambda x: x[1]['double_probability'], reverse=True)
        
        recommendations = []
        for i, (stock_code, info) in enumerate(sorted_stocks[:top_n]):
            recommendations.append({
                'rank': i + 1,
                'stock_code': stock_code,
                'stock_name': info['stock_name'],
                'current_price': info['current_price'],
                'double_probability': info['double_probability'],
                'probability_percent': info['double_probability'] * 100,
                'latest_date': info['latest_date']
            })
        
        return recommendations

class StockDataset(Dataset):
    """股票数据集"""
    
    def __init__(self, sequences, targets):
        self.sequences = torch.FloatTensor(sequences)
        self.targets = torch.FloatTensor(targets)
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return self.sequences[idx], self.targets[idx]

if __name__ == "__main__":
    # 测试优化模型
    from data_analyzer import StockDataAnalyzer
    
    print("加载数据...")
    analyzer = StockDataAnalyzer('缺口股票时序数据集_20250625_213940.csv')
    data = analyzer.process_all_stocks()
    
    print("训练优化模型...")
    predictor = OptimizedStockPredictor(sequence_length=40, prediction_weeks=50)
    metrics = predictor.train_with_kfold(data, n_splits=3, epochs=100)
    
    print("生成预测...")
    predictions = predictor.predict(data)
    recommendations = predictor.get_top_recommendations(predictions, top_n=10)
    
    print("\n=== 优化后推荐股票 ===")
    for rec in recommendations:
        print(f"{rec['rank']}. {rec['stock_code']} {rec['stock_name']}")
        print(f"   当前价格: {rec['current_price']:.2f}")
        print(f"   翻倍概率: {rec['probability_percent']:.2f}%")
        print(f"   最新日期: {rec['latest_date']}")
        print()
